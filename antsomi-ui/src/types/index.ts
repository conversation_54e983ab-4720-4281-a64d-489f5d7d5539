export type THandleErrorPayload = {
  path: string;
  name: string;
  args: Record<string, any>;
};

export type TLocale = 'en' | 'vi';

export type GenericFunction = (...args: any[]) => any;

export type TConfigErrorInfo = {
  [key: string]: any;
  path?: string;
  action?: string;
  name?: string;
  args?: any;
  priority?: 'low' | 'medium' | 'high';
  from?: 'try-catch' | 'error-boundary';
};

export * from './richMenu';
export * from './variables';
export * from './service';
export * from './templateListing';
export * from './share-access';
export * from './processingNotification';
export * from './postMessage';
export * from './actionsButton';
export * from './condition';
export * from './api';
export * from './date';
export * from './auth';

export type PayloadInfo = {
  url?: string;
  userId?: string;
  token?: string;
  accountId?: string;
  portalId?: number;
};

export type TServiceAuth = PayloadInfo;

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
