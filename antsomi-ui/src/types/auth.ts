/**
 * Represents an authentication info with associated user and portal information.
 */
export interface Auth {
  /**
   * The authentication token.
   */
  token: string;
  /**
   * The ID of the authenticated user.
   */
  userId: number;
  /**
   * The ID of the portal associated with the authentication token.
   */
  portalId?: number;
  /**
   * The ID of the account associated with the authentication token.
   */
  accountId?: number;
  /**
   * The user's preferred language.
   */
  language?: string;
}
