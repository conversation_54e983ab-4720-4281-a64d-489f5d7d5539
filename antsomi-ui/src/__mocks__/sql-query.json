{"queries": [{"name": "<PERSON><PERSON><PERSON> nhân viên có mức lương cao thứ hai của mỗi phòng ban", "description": "Sử dụng Window Function DENSE_RANK() để xếp hạng nhân viên theo lương trong mỗi phòng ban và lấy ra những người có thứ hạng là 2.", "query": "-- Sử dụng Window Function DENSE_RANK() để xếp hạng nhân viên theo lương trong mỗi phòng ban.\n-- DENSE_RANK() sẽ gán cùng một thứ hạng cho các nhân viên có cùng mức lương.\n-- <PERSON><PERSON> đó, chúng ta chỉ cần chọn những nhân viên có thứ hạng là 2.\nSELECT \n    e.name,\n    d.name AS department_name,\n    e.salary\nFROM (\n    SELECT\n        e.name,\n        e.salary,\n        e.department_id,\n        d.name AS department_name,\n        -- Xếp hạng nhân viên theo mức lương giảm dần trong từng phòng ban.\n        -- PARTITION BY chia dữ liệu thành các nhóm theo department_id.\n        -- ORDER BY sắp xếp các dòng trong mỗi nhóm theo salary.\n        DENSE_RANK() OVER (PARTITION BY e.department_id ORDER BY e.salary DESC) as salary_rank\n    FROM \n        employees e\n    JOIN \n        departments d ON e.department_id = d.id\n) as ranked_employees\nWHERE \n    ranked_employees.salary_rank = 2;"}, {"name": "<PERSON><PERSON><PERSON> tổng doanh số tích lũy của mỗi nhân viên theo thời gian", "description": "Sử dụng Window Function SUM() với ORDER BY để tính tổng doanh số tích lũy (Running Total) cho từng nhân viên theo ngày bán hàng.", "query": "-- Sử dụng Window Function SUM() với ORDER BY để tính tổng doanh số tích lũy (Running Total).\n-- <PERSON><PERSON><PERSON><PERSON> này cho phép chúng ta tính tổng của một cột trong một cửa sổ dữ liệu được sắp xếp.\nSELECT\n    s.employee_id,\n    s.sale_date,\n    s.amount,\n    -- Tính tổng doanh số tích lũy cho mỗi nhân viên.\n    -- PARTITION BY employee_id đảm bảo tổng chỉ tính trong phạm vi của từng nhân viên.\n    -- ORDER BY sale_date sắp xếp các giao dịch theo thời gian.\n    -- Cửa sổ mặc định sẽ là từ đầu partition đến dòng hiện tại.\n    SUM(s.amount) OVER (PARTITION BY s.employee_id ORDER BY s.sale_date) as cumulative_sales\nFROM\n    sales s\nORDER BY \n    s.employee_id, \n    s.sale_date;"}, {"name": "Tì<PERSON> phòng ban có doanh số cao hơn mức trung bình của tất cả các phòng ban", "description": "<PERSON><PERSON><PERSON> hợp Common Table Expression (CTE) để tính tổng doanh số của mỗi phòng ban, sau đó dùng subquery để so sánh với mức trung bình chung.", "query": "-- Sử dụng Common Table Expression (CTE) để tính tổng doanh số của mỗi phòng ban trước.\n-- <PERSON><PERSON><PERSON><PERSON> này giúp cho truy vấn chính trở nên gọn gàng và dễ hiểu hơn.\nWITH DepartmentSales AS (\n    -- Bước 1: <PERSON><PERSON><PERSON> tổng doanh số của mỗi phòng ban.\n    SELECT \n        d.id AS department_id,\n        d.name AS department_name,\n        SUM(s.amount) as total_department_sales\n    FROM \n        departments d\n    JOIN \n        employees e ON d.id = e.department_id\n    JOIN \n        sales s ON e.id = s.employee_id\n    GROUP BY \n        d.id, d.name\n)\n-- Bước 2: T<PERSON>y vấn chính. Lấy những phòng ban có tổng doanh số lớn hơn mức trung bình của tất cả các phòng ban.\nSELECT\n    department_name,\n    total_department_sales\nFROM \n    DepartmentSales\nWHERE\n    total_department_sales > (\n        -- Subquery: <PERSON><PERSON><PERSON> mức trung bình của tổng doanh số của tất cả các phòng ban.\n        -- <PERSON><PERSON>g ta không thể sử dụng trực tiếp AVG() trên total_department_sales ở đây.\n        SELECT \n            AVG(total_department_sales)\n        FROM \n            DepartmentSales\n    );"}, {"name": "<PERSON><PERSON><PERSON> nhân viên không có giao dịch bán hàng trong 30 ngày gần nhất", "description": "Sử dụng LEFT JOIN từ bảng nhân viên đến bảng doanh số và dùng HAVING cùng hàm DATEDIFF() để lọc ra những nhân viên không có giao dịch gần đây.", "query": "-- T<PERSON><PERSON> nhân viên không có giao dịch nào trong 30 ngày gần nhất.\n-- Bước 1: LEFT JOIN từ bảng employees đến bảng sales.\n-- Đ<PERSON><PERSON><PERSON> này đảm bảo tất cả nhân viên đều được hiển thị, ngay cả khi họ không có giao dịch nào.\nSELECT \n    e.name,\n    e.department_id,\n    e.salary\nFROM \n    employees e\nLEFT JOIN \n    sales s ON e.id = s.employee_id\nGROUP BY \n    e.id, e.name, e.department_id, e.salary\n-- Bước 2: Sử dụng HAVING để lọc các nhóm (nhân viên)\n-- có giao dịch gần nhất (MAX(s.sale_date)) đã lâu hơn 30 ngày so với ngày hiện tại (CURDATE()).\n-- COALESCE() đư<PERSON>c sử dụng để xử lý trường hợp nhân viên chưa bao giờ có giao dịch nào,\n-- khi đó MAX(s.sale_date) sẽ là NULL. COALESCE sẽ thay thế NULL bằng một ngày rất xa trong quá khứ\n-- để đảm bảo điều kiện DATEDIFF() luôn đúng.\nHAVING \n    COALESCE(MAX(s.sale_date), '1900-01-01') < CURDATE() - INTERVAL 30 DAY;"}, {"name": "<PERSON>ân loại nhân viên dựa trên mức l<PERSON> so với trung bình của phòng ban", "description": "Sử dụng Window Function AVG() OVER() để tính mức lương trung bình của từng phòng ban, sau đó dùng CASE statement để phân loại từng nhân viên.", "query": "-- <PERSON>ân loại nhân viên dựa trên mức lương của họ so với mức lương trung bình của phòng ban.\nSELECT \n    e.name,\n    d.name AS department_name,\n    e.salary,\n    -- Sử dụng CASE statement để tạo một phân loại mới.\n    -- <PERSON><PERSON><PERSON> lương của từng nhân viên đượ<PERSON> so sánh với mức lương trung bình của phòng ban.\n    CASE\n        -- AVG(e.salary) OVER (PARTITION BY e.department_id) tính mức lương trung bình của mỗi phòng ban.\n        -- <PERSON><PERSON><PERSON> là một Window Function, gi<PERSON><PERSON> chúng ta tính toán giá trị tổng hợp (AVG) mà không làm mất các dòng dữ liệu chi tiết.\n        WHEN e.salary > AVG(e.salary) OVER (PARTITION BY e.department_id) THEN 'Above Average'\n        WHEN e.salary < AVG(e.salary) OVER (PARTITION BY e.department_id) THEN 'Below Average'\n        ELSE 'Average'\n    END AS salary_category\nFROM \n    employees e\nJOIN \n    departments d ON e.department_id = d.id;"}], "queries_optimization": [{"name": "Query 1: Top 3 lương cao nhất mỗi phòng ban (Correlated Subquery)", "description": "<PERSON><PERSON><PERSON> là cách tiếp cận cổ điển nhưng kém hiệu quả nhất, sử dụng một subquery phụ thuộc vào truy vấn chính. Subquery này sẽ chạy lại cho mỗi dòng của bảng <PERSON>, gây tốn tài nguyên trên dữ liệu lớn.", "query": "-- Sử dụng Correlated Subquery để tìm 3 nhân viên có lương cao nhất.\n-- Subquery bên trong sẽ đếm số lượng nhân viên trong cùng phòng ban\n-- c<PERSON> mức lương cao hơn hoặc bằng nhân viên hiện tại.\n-- Nếu số lượng đó nhỏ hơn 3, nhân viên hiện tại sẽ được chọn.\nSELECT\n\te1.name,\n\te1.salary,\n\td.name AS department_name\nFROM\n\temployees e1\nJOIN\n\tdepartments d ON e1.department_id = d.id\nWHERE\n\t3 > (SELECT\n\t\tCOUNT(DISTINCT e2.salary)\n\tFROM\n\t\temployees e2\n\tWHERE\n\t\te2.department_id = e1.department_id\n\t\tAND e2.salary > e1.salary\n\t);"}, {"name": "Query 2: Top 3 lư<PERSON><PERSON> cao nhất mỗi phòng ban (Self-JOIN)", "description": "<PERSON><PERSON><PERSON> cách tiếp cận cũ khác sử dụng Self-JOIN. <PERSON><PERSON><PERSON> là một cải tiến nhỏ so với subquery nhưng vẫn phức tạp và khó đọc. <PERSON><PERSON><PERSON> suất cũng không cao trên dữ liệu lớn.", "query": "-- <PERSON><PERSON> dụng kỹ thuật Self-JO<PERSON> để so sánh mỗi nhân viên với những người khác\n-- trong cùng phòng ban có mức lương cao hơn.\n-- <PERSON><PERSON> đ<PERSON>, GROUP BY và COUNT() để lọc ra những nhân viên nằm trong top 3.\nSELECT\n\te1.name,\n\te1.salary,\n\td.name AS department_name\nFROM\n\temployees e1\nJOIN\n\tdepartments d ON e1.department_id = d.id\nLEFT JOIN\n\temployees e2 ON e1.department_id = e2.department_id AND e1.salary < e2.salary\nGROUP BY\n\te1.id, e1.name, e1.salary, d.name\nHAVING\n\tCOUNT(DISTINCT e2.salary) < 3\nORDER BY\n\td.name, e1.salary DESC;"}, {"name": "Query 3: Top 3 lư<PERSON>ng cao nhất mỗi phòng ban (Window Function: RANK())", "description": "<PERSON><PERSON><PERSON> là cách hiện đại và đư<PERSON><PERSON> khu<PERSON>ến nghị nhất. Sử dụng Window Function giúp truy vấn hiệu quả và dễ đọc hơn rất nhiều. RANK() sẽ xếp hạng các nhân viên và bỏ qua các số thứ tự nếu có các giá trị bằng nhau.", "query": "-- C<PERSON>ch hiện đại và hiệu quả nhất dùng Window Function RANK() trong một CTE (Common Table Expression).\n-- CTE giúp truy vấn dễ đọc hơn bằng cách tạo một tập kết quả tạm thời có tên.\nWITH RankedEmployees AS (\n\tSELECT\n\t\te.name,\n\t\te.salary,\n\t\td.name AS department_name,\n\t\t-- RANK() xếp hạng nhân viên theo lương trong từng phòng ban.\n\t\t-- PARTITION BY chia dữ liệu thành các nhóm theo department_id.\n\t\t-- ORDER BY sắp xếp các dòng trong mỗi nhóm theo salary DESC.\n\t\tRANK() OVER (PARTITION BY e.department_id ORDER BY e.salary DESC) as rnk\n\tFROM\n\t\temployees e\n\tJOIN\n\t\tdepartments d ON e.department_id = d.id\n)\n-- T<PERSON>y vấn chính sẽ chọn ra các nhân viên có thứ hạng <= 3 từ CTE.\nSELECT\n\tname,\n\tsalary,\n\tdepartment_name\nFROM\n\tRankedEmployees\nWHERE\n\trnk <= 3\nORDER BY\n\tdepartment_name, salary DESC;"}, {"name": "Query 4: Top 3 lư<PERSON>ng cao nhất mỗi phòng ban (Window Function: DENSE_RANK())", "description": "<PERSON><PERSON><PERSON> biến thể của Query 3, sử dụng DENSE_RANK(). <PERSON>ứ<PERSON> năng này tương tự RANK() nhưng sẽ không bỏ qua các số thứ tự khi có các giá trị bằng nhau, phù hợp cho nhiều trư<PERSON><PERSON> hợp phân tích hơn.", "query": "-- <PERSON><PERSON><PERSON><PERSON> thể của cách trên, dùng DENSE_RANK() thay vì RANK().\n-- DENSE_RANK() sẽ không bỏ qua các số thứ tự khi có các giá trị bằng nhau.\nWITH RankedEmployees AS (\n\tSELECT\n\t\te.name,\n\t\te.salary,\n\t\td.name AS department_name,\n\t\t-- DENSE_RANK() xếp hạng nhân viên, không bỏ qua các số thứ tự.\n\t\tDENSE_RANK() OVER (PARTITION BY e.department_id ORDER BY e.salary DESC) as rnk\n\tFROM\n\t\temployees e\n\tJOIN\n\t\tdepartments d ON e.department_id = d.id\n)\n-- <PERSON><PERSON><PERSON> vấn chính sẽ chọn ra các nhân viên có thứ hạng <= 3.\nSELECT\n\tname,\n\tsalary,\n\tdepartment_name\nFROM\n\tRankedEmployees\nWHERE\n\trnk <= 3\nORDER BY\n\tdepartment_name, salary DESC;"}, {"name": "Query 5: Top 3 lương cao nhất mỗi phòng ban (Với JSON output)", "description": "<PERSON><PERSON><PERSON> là một cách tiếp cận nâng cao hơn, kế<PERSON> hợp Window Function và các hàm JSON để nhóm dữ liệu thành một định dạng JSON duy nhất. R<PERSON>t hữu ích cho việc tạo API hoặc xử lý dữ liệu phức tạp trên tầng ứng dụng.", "query": "-- <PERSON><PERSON><PERSON> hợp Window Function với hàm JSON để nhóm dữ liệu thành một định dạng JSON duy nhất.\n-- Rất hữu ích khi bạn muốn trả về dữ liệu có cấu trúc từ database mà không cần xử lý thêm ở tầng ứng dụng.\nWITH RankedEmployees AS (\n\tSELECT\n\t\te.name,\n\t\te.salary,\n\t\td.name AS department_name,\n\t\tROW_NUMBER() OVER (PARTITION BY e.department_id ORDER BY e.salary DESC) as rnk\n\tFROM\n\t\temployees e\n\tJOIN\n\t\tdepartments d ON e.department_id = d.id\n)\nSELECT\n\tdepartment_name,\n\t-- Sử dụng JSON_ARRAYAGG để tổng hợp các nhân viên vào một mảng JSON.\n\tJSON_ARRAYAGG(\n\t\tJSON_OBJECT('name', name, 'salary', salary)\n\t) AS top_employees\nFROM\n\tRankedEmployees\nWHERE\n\trnk <= 3\nGROUP BY\n\tdepartment_name\nORDER BY\n\tdepartment_name;"}]}