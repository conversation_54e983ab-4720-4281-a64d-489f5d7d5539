/* eslint-disable no-param-reassign */
// Libraries
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useImmer } from 'use-immer';
import _ from 'lodash';

// Components & Styled
import { Emoji, EmojiList, EmptyEmoji, WrapperCollection } from '../styled';
import {
  Button,
  Flex,
  Input,
  Scrollbars,
  Typography,
} from '@antscorp/antsomi-ui/es/components/atoms';
import { ICON_EMOJI_COMMON, Select } from '@antscorp/antsomi-ui/es/components/molecules';
import { EmojiSmileIcon, SearchIcon } from '@antscorp/antsomi-ui/es/components/icons';

// Hooks
import { useDebounce } from '@antscorp/antsomi-ui/es/hooks/useDebounceV2';

// Constants
import { COMMON_TAB_COLLECTION, MIN_VISIBLE_COUNT, SMILEYS_BODY } from './constants';
import { globalToken } from '@antscorp/antsomi-ui/es/constants';

// Types
import type { EmojiCollectionProps } from '../types';

const { Text } = Typography;

const emojiListParsed = JSON.parse(JSON.stringify(ICON_EMOJI_COMMON));

const CommonCollection: React.FC<EmojiCollectionProps> = ({ onEmojiClick }) => {
  // States
  const [visibleCount, setVisibleCount] = useState(MIN_VISIBLE_COUNT);
  const [state, setState] = useImmer<{ collectionActive: string; txtSearch: string }>({
    collectionActive: SMILEYS_BODY,
    txtSearch: '',
  });

  const txtSearchDebounce = useDebounce(state.txtSearch, 300);

  // Memoizations
  const collectionOptions = useMemo(() => Object.values(COMMON_TAB_COLLECTION), []);

  const emojiCollection = useMemo(() => {
    if (!_.isArray(emojiListParsed)) return [];

    const listFiltered = emojiListParsed.filter(item => item.slug === state.collectionActive);

    let result = _.get(listFiltered, '[0].emojis', []);

    if (txtSearchDebounce) {
      result = result.filter(item => {
        const { emoji = '', name = '' } = item;
        const isEmoji = emoji.toLowerCase().includes(txtSearchDebounce.toLowerCase());

        const isName = name.toLowerCase().includes(txtSearchDebounce.toLowerCase());

        return isEmoji || isName;
      });
    }

    return result;
  }, [state.collectionActive, txtSearchDebounce]);

  const handleChangeInput = useCallback(
    (e: any) => {
      if (e.target) {
        setState(draft => {
          draft.txtSearch = e.target.value.trim();
        });
      }
    },
    [setState],
  );

  const onScroll = useCallback((e: any, collectionLength: number) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;
    if (scrollHeight - scrollTop <= clientHeight * 1.5) {
      setVisibleCount(prev => {
        const newCount = prev + 30;
        if (newCount >= collectionLength)
          return collectionLength > MIN_VISIBLE_COUNT ? collectionLength : MIN_VISIBLE_COUNT;

        return newCount;
      });
    }
  }, []);

  const handleChangeSelect = useCallback(
    (newCollection: string) => {
      setState(draft => {
        draft.collectionActive = newCollection;
      });
      setVisibleCount(prev => prev + 10);
    },
    [setState],
  );

  useEffect(
    () => () => {
      setState(() => ({
        collectionActive: SMILEYS_BODY,
        txtSearch: '',
      }));
    },
    [setState],
  );

  const renderOption = useCallback((rawOption: Record<string, any>) => {
    const icon = _.get(rawOption, 'data.icon', null);

    return (
      <Flex gap={6} align="center">
        <span
          style={{
            color: globalToken?.bw8,
            fontSize: '20px',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          {icon}
        </span>
        <Text style={{ color: globalToken?.colorText }}>{rawOption?.label}</Text>
      </Flex>
    );
  }, []);

  const renderCommonCollection = useCallback(() => {
    if (
      !_.isArray(emojiCollection) ||
      (_.isArray(emojiCollection) && emojiCollection.length === 0)
    ) {
      return (
        <EmptyEmoji
          image={
            <Button
              type="text"
              shape="round"
              icon={<EmojiSmileIcon color={globalToken?.bw5} style={{ width: 30, height: 30 }} />}
              style={{ width: 60, height: 60, background: globalToken?.bw2 }}
            />
          }
          imageStyle={{ height: 60, marginBottom: 15 }}
          description={
            <span style={{ color: globalToken?.bw8 }}>No emoji matches your keyword</span>
          }
        />
      );
    }

    const list = emojiCollection.slice(0, Math.min(visibleCount, emojiCollection.length));

    const content = list.map((item: Record<string, any>) => {
      const { emoji, slug = '', unicode_version: unicodeVersion = '' } = item;
      const key = `${slug}-${unicodeVersion}`;

      return (
        <Emoji key={key} type="text" onClick={() => onEmojiClick(emoji)}>
          <span style={{ fontSize: '30px' }}>{emoji}</span>
        </Emoji>
      );
    });

    return (
      <Scrollbars
        autoHeight
        autoHeightMax={150}
        onScroll={e => onScroll(e, emojiCollection.length)}
      >
        <Flex wrap="wrap">{content}</Flex>
      </Scrollbars>
    );
  }, [emojiCollection, visibleCount, onScroll, onEmojiClick]);

  return (
    <WrapperCollection>
      <Flex gap={20} justify="space-between" align="flex-end">
        <Input
          placeholder="Search..."
          suffix={<SearchIcon color={globalToken?.bw8} />}
          styles={{ affixWrapper: { minWidth: 280 } }}
          onChange={handleChangeInput}
        />
        <Select
          placeholder="Select collection"
          value={state.collectionActive}
          style={{ width: 200 }}
          options={collectionOptions}
          optionRender={renderOption}
          onChange={handleChangeSelect}
        />
      </Flex>
      <EmojiList>{renderCommonCollection()}</EmojiList>
    </WrapperCollection>
  );
};

CommonCollection.defaultProps = {
  onEmojiClick: () => {},
};

export default memo(CommonCollection);
