// Libraries
import stringReplaceToArray from 'string-replace-to-array';
import _ from 'lodash';
import Tagify from '@yaireo/tagify';

// Types
import { AcceptablePattern, EmojiCollection, EmojiTag, TagDataCustomize, TagType } from './types';

// Constants
import {
  EMOJI_COLLECTIONS,
  PREFIX_PATTERN_LINE_MESSAGE,
  READONLY_TAG,
  SHORT_LINK_V2,
  TAG_TYPE,
} from './constants';
import { iconsViber } from './iconsViber';

// Utils
import {
  acceptablePatternChecking,
  detectURLRegex,
  getCachedRegex,
  patternHandlers,
  tagRegexStringPattern,
} from './patternHandlers';

const {
  CUSTOMER,
  VISITOR,
  EVENT,
  JOURNEY,
  CAMPAIGN,
  VARIANT,
  PROMOTION_CODE,
  CUSTOM_FN,
  OBJECT_WIDGET,
  CONTENT_SOURCE_GROUP,
} = TAG_TYPE;

/*
 * Custom error type for JSON parse errors
 */
class JsonParseError extends Error {
  constructor(
    message: string,
    public originalError?: Error,
    public partialResult?: any,
  ) {
    super(message);
    this.name = 'JsonParseError';
  }
}

/**
 * Parses the input string and replaces matching patterns with processed tags.
 * This function iterates over predefined regex patterns and replaces each match
 * with a corresponding replacement string generated by pattern handlers.
 *
 * @param {string} input - The input string containing tags and patterns to be replaced.
 * @param {Array<AcceptablePattern>} acceptableTagPattern - An array of acceptable tag patterns
 *        passed to the pattern handler for tag processing.
 * @returns {string} The modified string with all pattern matches replaced by their corresponding tags.
 *
 * The function workflow:
 * - Collects all matches of each regex pattern within the input string.
 * - Stores the matches along with their positions and replacement strings.
 * - Sorts the matches by their start positions to maintain correct order.
 * - Constructs the final output by replacing matched segments with the corresponding replacement tags.
 *
 * Example:
 * ```javascript
 * const input = "Here is some text with a #{shortlink(https://example.com)} pattern.";
 * const acceptableTagPattern = ['shortlink'];
 * const result = parseTagStringToTagify(input, acceptableTagPattern);
 * ```
 */
export const parseTagStringToTagify = (
  input: string,
  acceptableTagPattern: Array<AcceptablePattern>,
): string => {
  const resultParts: string[] = [];

  // Array to store all matches from all patterns with their positions
  const matches: { startIndex: number; endIndex: number; replacement: string }[] = [];

  // Collect all matches from each pattern
  Object.values(patternHandlers).forEach(patternWrapper => {
    const {
      pattern,
      name: cachePatternName,
      acceptablePattern: acceptableType,
      handler,
    } = patternWrapper;

    const isAccepted = acceptablePatternChecking(acceptableType, acceptableTagPattern);

    // No need to continue if pattern is not accepted
    if (!isAccepted) return;

    // Use the cached regex instead of creating a new one each time
    const regex = getCachedRegex(pattern, 'g', cachePatternName);
    let match: RegExpExecArray | null;

    // Iterate over matches of the current pattern
    // eslint-disable-next-line no-cond-assign
    while ((match = regex.exec(input)) !== null) {
      const { isValid, tag } = handler(match);

      if (isValid) {
        matches.push({ startIndex: match.index, endIndex: regex.lastIndex, replacement: tag });
      }
    }
  });

  // Sort matches by their startIndex to process them in correct order
  matches.sort((a, b) => a.startIndex - b.startIndex);

  let lastIndex = 0;
  // Iterate over sorted matches to build the final result
  matches.forEach(({ startIndex, endIndex, replacement }) => {
    // Some tags, such as Individual Shortlink can include custom tags.
    const isInOtherTag = matches.some(
      compareTag => compareTag.startIndex < startIndex && endIndex < compareTag.endIndex,
    );

    if (isInOtherTag) return;

    // Add text before the current match
    resultParts.push(input.slice(lastIndex, startIndex));
    // Add the replacement string
    resultParts.push(replacement);
    // Update lastIndex for the next loop
    lastIndex = endIndex;
  });

  // Append any remaining text after the last match
  resultParts.push(input.slice(lastIndex));

  // Combine all parts into the final result string
  return resultParts.join('').trimEnd();
};

/**
 * Validates whether the parsed object contains the required "label" and "value" fields.
 * @param tag The parsed tag object.
 * @returns True if valid, false otherwise.
 */
function isValidTag(tag: TagDataCustomize) {
  return tag && typeof tag === 'object' && 'label' in tag && 'value' in tag;
}

/**
 * Attempts to parse a JSON string with robust error handling
 * @param jsonString The JSON string to parse
 * @param options Configuration options
 * @returns Parsed JSON object or null if parsing fails and throwOnError is false
 * @throws JsonParseError if parsing fails and throwOnError is true
 */
function robustJsonParse(
  jsonString: string,
  options: {
    sanitize?: boolean;
    throwOnError?: boolean;
    allowPartialParse?: boolean;
  } = {},
): any {
  const { sanitize = true, throwOnError = false, allowPartialParse = false } = options;
  let sanitizedString = jsonString;

  // Helper function to sanitize the JSON string
  const sanitizeJson = (str: string): string =>
    str
      .replace(/'/g, '"') // Replace single quotes with double quotes
      .replace(/([{,]\s*)(\w+)(\s*:)/g, '$1"$2"$3') // Add quotes to unquoted keys
      .replace(/,\s*([\]}])/g, '$1') // Remove trailing commas
      .replace(/\\'/g, "'") // Unescape single quotes within strings
      .trim(); // Trim whitespace

  const tryParse = (str: string): { result: string | null; error: Error | null } => {
    try {
      return { result: JSON.parse(str), error: null };
    } catch (e) {
      return { result: null, error: e as Error };
    }
  };

  // First, try parsing the string as-is
  let parseResult = tryParse(jsonString);
  if (!parseResult.error) {
    return parseResult.result;
  }

  // If sanitize option is true, try sanitizing and parsing again
  if (sanitize) {
    sanitizedString = sanitizeJson(jsonString);
    parseResult = tryParse(sanitizedString);

    if (!parseResult.error) {
      return parseResult.result;
    }
  }

  // If allowPartialParse is true, attempt to salvage partial data
  if (allowPartialParse) {
    const partialJson = sanitizedString.replace(/[^}]*$/, '}');

    parseResult = tryParse(partialJson);

    if (!parseResult.error) {
      // eslint-disable-next-line no-console
      console.warn('Parsed partial JSON data. Some information may be missing.', {
        result: parseResult.result,
      });

      return parseResult.result;
    }
  }

  // If we've reached this point, all parsing attempts have failed
  const errorMessage = `Failed to parse JSON${sanitize ? ' even after sanitization' : ''}${allowPartialParse ? ' and partial parsing' : ''}: ${parseResult.error.message}`;

  if (throwOnError) {
    throw new JsonParseError(errorMessage, parseResult.error, null);
  } else {
    // eslint-disable-next-line no-console
    console.error(errorMessage);

    return null;
  }
}

/**
 * Formats the parsed tag object into a string.
 * @param tag The parsed tag object containing "label" and "value".
 * @returns The formatted string.
 */
function formatTag(tag: TagDataCustomize): string {
  return tag.value;
}

/**
 * Converts an input string containing JSON-like tags to a formatted string.
 * @param input The input string with JSON-like tags.
 * @returns The formatted output string.
 */
export function convertInputStringToOriginal(input: string): string {
  // Regex to match JSON-like tags
  const tagRegex = getCachedRegex(tagRegexStringPattern, 'g', 'convertInputStringToOriginal');

  const textFormated = input.replace(tagRegex, (_, jsonString) => {
    let parsedJsonString: any;

    try {
      parsedJsonString = robustJsonParse(jsonString, {
        sanitize: true,
        throwOnError: true,
      });

      // If the parsed string is not a valid tag, fallback to original string
      if (!isValidTag(parsedJsonString)) {
        parsedJsonString = null;
      }
    } catch (error) {
      if (error instanceof JsonParseError) {
        // eslint-disable-next-line no-console
        console.error('Parsing failed:', error.message);

        if (error.partialResult) {
          // eslint-disable-next-line no-console
          console.log('Partial result:', error.partialResult);
        }
      } else {
        // eslint-disable-next-line no-console
        console.error('Unexpected error:', error);
      }

      // Fall back to original string if parsing fails
      parsedJsonString = null;
    }

    // Return the original string if parsing fails or is not a valid tag
    if (!parsedJsonString) {
      // eslint-disable-next-line no-console
      console.error('Error parsing tag:', jsonString);
      return _;
    }

    return formatTag(parsedJsonString);
  });

  // Remove Zero Width Space characters (Unicode U+200B)
  const cleanedString = textFormated.replace(/\u200B/g, '');

  // Remove extra newline at the end
  // - see: https://github.com/yairEO/tagify/blob/master/src/tagify.js#L1822
  // - at function: [getMixedTagsAsString]
  return cleanedString.replace(/\r\n$/, '').trim();
}

export const getLinkLineURLImage = (productId: string = '', code: string = '') =>
  `https://stickershop.line-scdn.net/sticonshop/v1/sticon/${productId}/android/${code}.png?detect-emoji=line-message&v=1`;

export const getImageSourceViberEmoji = (fileName: string = '') => {
  // eslint-disable-next-line global-require, import/no-dynamic-require
  const src = require(`../../../assets/images/viber/96x96/${fileName}`);
  return src.default || src;
};

/**
 * Creates a renderer function for Viber emojis that maps specific filenames to emojis.
 *
 * @returns {Function} A function that takes a filename extension and returns a corresponding image source URL
 * or undefined if the image path is not found.
 *
 */
const makeViberEmojiRenderer = (): Function =>
  function renderViberEmoji(fileNameExtension: string): string | undefined {
    const src = getImageSourceViberEmoji(fileNameExtension);
    return src;
  };

/**
 * Creates a renderer function for Line message emojis.
 *
 * @returns {Function} A function that accepts an object containing `productId` and `code`, then returns
 * a URL for the Line emoji image. If an error occurs, it returns an empty function.
 *
 * The function attempts to generate a source URL based on product information, and catches errors during rendering.
 */
const makeLineMessageEmojiRenderer = (): Function => {
  try {
    return function renderLineMessageEmoji(infoUrl: Record<string, any>) {
      const { productId = '', code: codeImage = '' } = infoUrl;
      const src = getLinkLineURLImage(productId, codeImage);
      return src;
    };
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);
    return () => {};
  }
};

/**
 * Retrieves a source function based on the specified emoji collection type.
 *
 * @param {EmojiCollection} collectionType - The type of emoji collection (e.g., LINE, VIBER).
 * @returns {Function} A function that handles rendering of emojis for the specified collection type,
 * or an empty function if the collection type is not recognized.
 *
 * The function switches between different emoji renderers depending on the collection type and
 * returns the appropriate rendering function.
 */
const getSourceEmojiCollection = (collectionType: EmojiCollection): Function => {
  let getSourceFn: Function = () => {};

  try {
    switch (collectionType) {
      case EMOJI_COLLECTIONS.LINE: {
        getSourceFn = makeLineMessageEmojiRenderer();
        break;
      }
      case EMOJI_COLLECTIONS.VIBER: {
        getSourceFn = makeViberEmojiRenderer();
        break;
      }
      default: {
        break;
      }
    }
    return getSourceFn;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);
    return getSourceFn;
  }
};

/**
 * Replaces Line message emojis in the given text with their corresponding URLs.
 *
 * @param {string} [text=''] - The input text containing emoji patterns to replace.
 * @param {Function} [getSourceUrl=() => {}] - A function to get the source URL for each matched emoji.
 * @returns {string[]} An array of strings and/or replaced emoji URLs.
 *
 * This function uses a regex pattern to identify Line message emojis in the text and replace them with
 * their corresponding image URLs using the provided `getSourceUrl` function.
 */
const replaceLineMessageEmoji = (
  text: string = '',
  getSourceUrl: Function = () => {},
): string[] => {
  try {
    const regexLineReplace = getCachedRegex(
      `\\$\\(\\(${PREFIX_PATTERN_LINE_MESSAGE}:(.*?):(.*?)\\)\\)`,
      'g',
      'replacingLineEmoji',
    );

    // Note: the latest regex added an empty matching group, so we ignore it.
    return stringReplaceToArray(
      text,
      regexLineReplace,
      (match: string, productId: string | number, code: string, ...restProps: any[]) =>
        getSourceUrl({ productId, code }, match, restProps),
    );
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);
    return [];
  }
};

/**
 * Replaces Viber emojis in the given text with their corresponding URLs.
 *
 * @param {string} text - The input text containing emoji patterns to replace.
 * @param {Function} getSourceUrl - A function to get the source URL for each matched emoji.
 * @returns {string[]} An array of strings and/or replaced emoji URLs.
 *
 * The function uses a regex pattern to identify Viber emojis in the text and replace them with
 * their corresponding image URLs using the provided `getSourceUrl` function.
 */
const replaceViberEmoji = (text: string, getSourceUrl: Function): string[] => {
  const regexViberReplace = getCachedRegex(
    '\\(([\\w!@#$%^&*-=+`~{}?,]+)\\)',
    'gi',
    'replacingViberEmoji',
  );

  // Note: the latest regex added an empty matching group, so we ignore it.
  const result = stringReplaceToArray(text, regexViberReplace, (_match, name, _offset) => {
    const fileName = iconsViber[name] ? iconsViber[name] : `__${name}`;

    return getSourceUrl(fileName);
  });

  return result;
};

/**
 * Retrieves and replaces emojis in the text based on the specified emoji collection type.
 *
 * @param {EmojiCollection} collectionType - The type of emoji collection (e.g., LINE, VIBER).
 * @param {string} [text=''] - The input text containing emoji patterns to replace.
 * @param {Function} [getSourceUrl=() => {}] - A function to get the source URL for each matched emoji.
 * @returns {string[]} The modified text with replaced emojis.
 *
 * The function switches between Line and Viber emoji collections to replace their patterns
 * within the provided text using the `getSourceUrl` function.
 */
const getEmojiUrl = (
  collectionType: EmojiCollection,
  text: string = '',
  getSourceUrl: Function = () => {},
): string[] => {
  try {
    switch (collectionType) {
      case EMOJI_COLLECTIONS.LINE: {
        return replaceLineMessageEmoji(text, getSourceUrl);
      }
      case EMOJI_COLLECTIONS.VIBER: {
        return replaceViberEmoji(text, getSourceUrl);
      }
      default: {
        return [];
      }
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);
    return [];
  }
};

/**
 * Processes the input text to replace emojis based on the specified collection type.
 *
 * @param {string} text - The input text containing emoji patterns to replace.
 * @param {EmojiCollection} collectionType - The type of emoji collection to use for replacement (e.g., LINE, VIBER).
 * @returns {string[]} The modified text with replaced emojis.
 *
 * The function determines the appropriate emoji renderer based on the collection type and replaces
 * all matching emoji patterns in the input text with their corresponding URLs.
 */
export const emojiManufacturer = (text: string, collectionType: EmojiCollection): string[] => {
  try {
    const getSourceFn = getSourceEmojiCollection(collectionType);

    const url = getEmojiUrl(collectionType, text, getSourceFn);

    return url;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);
    return [];
  }
};

export const getEmojiTag = ({ src, emoji, code }: EmojiTag) => `
  <img
    src="${src}"
    data-emoji="${emoji}"
    data-emoji-code="${code}"
    alt="${emoji}"
    width="16px"
    height="16px"
    style="transform: translateY(3.5px); cursor: text;"
  />`;

export const isPersonalizeTagType = (type: TagType): boolean =>
  (
    [
      CUSTOMER,
      VISITOR,
      EVENT,
      JOURNEY,
      CAMPAIGN,
      VARIANT,
      PROMOTION_CODE,
      OBJECT_WIDGET,
      CUSTOM_FN,
      CONTENT_SOURCE_GROUP,
    ] as string[]
  ).includes(type);

export const isShortLinkTagType = (type: TagType): boolean =>
  ([SHORT_LINK_V2] as string[]).includes(type);

export const generateTagContent = (params: {
  tagData: TagDataCustomize;
  content: string;
}): string => {
  const { tagData, content } = params;

  let dataAttrsString = '';

  const dataAttrs = new Map<string, string>([['tag-type', tagData.type]]);

  if (tagData.type === TAG_TYPE.SHORT_LINK) {
    const { shortlinkType } = tagData;

    dataAttrs.set(`tag-${TAG_TYPE.SHORT_LINK}-type`, shortlinkType);
  }

  /** SHORT_LINK_V2 */
  if (tagData.type === TAG_TYPE.SHORT_LINK_V2) {
    const { shortlinkType } = tagData;

    dataAttrs.set(`tag-${TAG_TYPE.SHORT_LINK_V2}-type`, shortlinkType);
  }

  dataAttrs.forEach((value, key) => {
    dataAttrsString = dataAttrsString.concat(` data-${key}="${value}"`);
  });

  return `
    <div ${dataAttrsString}>
      ${content}
    </div>
  `;
};

/**
 * Unescapes specific HTML entities in a string.
 *
 * This function replaces the following HTML entities:
 * - '&quot;' with a double quote (")
 * - '&nbsp;' with a space
 * - 'nbsp;' with a space
 * - '&#x27;' with a single quote (')
 *
 * @param {string} str - The input string containing HTML entities to unescape.
 * @returns {string} The input string with specified HTML entities unescaped.
 *
 * @example
 * unescapeString('Hello &quot;world&quot; with&nbsp;spaces');
 * // returns 'Hello "world" with spaces'
 */
export const unescapeString = (str: string): string =>
  str.replace(/&quot;|&nbsp;|nbsp;|&#x27;|&amp;/gm, (match: string) => {
    switch (match) {
      case '&#x27;':
        return "'";
      case '&quot;':
        return '"';
      case '&nbsp;':
      case 'nbsp;':
        return ' ';
      case '&amp;':
        return '&';
      default:
        return match;
    }
  });

export function deepUnescape<T = any>(obj: T): T | string {
  // Base case: Return primitives as is
  if (obj === null || typeof obj !== 'object') {
    return typeof obj === 'string' ? unescapeString(obj) : obj;
  }

  // Handle arrays: Map and recurse
  if (Array.isArray(obj)) {
    return obj.map(deepUnescape) as T;
  }

  // Handle objects: Recurse into key-value pairs
  return Object.entries(obj).reduce(
    (result, [key, value]) => {
      result[key] = deepUnescape(value);
      return result;
    },
    {} as Record<string, unknown>,
  ) as T;
}

/**
 * Checks if the string is a valid tag type.
 * @param str
 *
 * @return boolean
 */
export const isValidTagType = (str: string) => Object.values(TAG_TYPE).includes(str as any);

/**
 * Checks if the string contains line break.
 *
 * @param str
 * @returns boolean
 */
export const hasLineBreak = (str: string) => str.includes('\n');

/**
 * Selects a range of text within a given DOM node.
 *
 * @param {Node} node - The DOM node containing the text to be selected.
 * @param {number} start - The starting index position of the selection.
 * @param {number} end - The ending index position of the selection.
 *
 * @example
 * const paragraph = document.querySelector('p');
 * if (paragraph.firstChild) {
 *   selectRange(paragraph.firstChild, 5, 10);
 * }
 *
 * @description
 * Creates a new DOM Range from the given start and end positions within the provided node
 * and applies it to the current window selection. This visually selects the specified
 * portion of text in the browser.
 *
 * Important considerations:
 * - The node should be a text node or a node that can contain text content
 * - The start and end indices must be valid positions within the node's text content
 * - The selection may not be visible if the containing element is not focusable
 * - Some browsers may prevent programmatic selection for security reasons
 * - Any errors during selection are caught and logged to the console
 *
 * Note that this function only creates a visual selection - it does not copy the text
 * or modify it in any way.
 */
export function selectRange(node: Node, start: number, end: number) {
  try {
    const range = document.createRange();
    range.setStart(node, start);
    range.setEnd(node, end);

    const selection = window.getSelection();

    if (selection) {
      selection.removeAllRanges();
      selection.addRange(range);
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('Error selecting range', error, { node, start, end });
  }
}

/**
 * Determines whether a tag is clickable based on its properties and the Tagify instance settings.
 *
 * @param {Tagify<TagDataCustomize>} tagify - The Tagify instance.
 * @param {HTMLElement} tag - The HTML element representing the tag.
 * @param {TagDataCustomize} tagData - The data associated with the tag.
 *
 * @returns {boolean} Returns true if the tag is clickable, false otherwise.
 *
 * @description
 * A tag is considered clickable if all of the following conditions are met:
 * 1. The Tagify instance is not in readonly mode.
 * 2. The tag itself is not marked as readonly.
 * 3. The tag type is valid (as determined by the isValidTagType function).
 * 4. The tag is not marked as emoji.
 */
export function isTagClickable(
  tagify: Tagify<TagDataCustomize>,
  tag: HTMLElement,
  tagData: TagDataCustomize,
): boolean {
  const { type } = tagData;

  const isEmojiTag = type === TAG_TYPE.EMOJI;
  if (isEmojiTag) {
    return false;
  }

  const isValidType = isValidTagType(type);

  const readonlyTag = tag.getAttribute(READONLY_TAG);
  const { readonly } = tagify.settings;

  return !(readonly || (readonlyTag && readonlyTag === 'true') || !isValidType);
}

/**
 * Finds text nodes within an HTML element that contain URLs.
 *
 * @param {HTMLSpanElement} element - The HTML span element to search within.
 * @param {number} [minLengthValidURL=8] - The minimum length of text content to consider for URL detection.
 *
 * @returns {Array<ChildNode>} An array of text nodes that contain URLs.
 *
 * @description
 * This function searches through the child nodes of the provided HTML element,
 * looking for text nodes that contain URLs. It uses the following criteria:
 * 1. Only text nodes are considered (nodeType === Node.TEXT_NODE).
 * 2. The text content of the node must be at least as long as minLengthValidURL.
 * 3. The text content must contain a URL as determined by the detectURLRegex.
 *
 * @example
 * const spanElement = document.querySelector('span.content');
 * const nodesWithURLs = findURLInTextNodes(spanElement);
 * console.log(nodesWithURLs.length + ' nodes contain URLs');
 *
 * @example
 * // With custom minimum length
 * const nodesWithLongURLs = findURLInTextNodes(spanElement, 15);
 *
 * @see detectURLRegex - This regex is used to detect URLs in the text content.
 */
export function findURLInTextNodes(
  element: HTMLSpanElement,
  minLengthValidURL: number = 8,
): Array<ChildNode> {
  const nodesURL = Array.from(element.childNodes).filter((node: ChildNode) => {
    // Exclude non-text nodes
    if (node.nodeType !== Node.TEXT_NODE) return false;

    // Only check nodes that contain at least 8 characters
    if (node.textContent && node.textContent.length >= minLengthValidURL) {
      // reset the regex's lastIndex before each execution
      detectURLRegex.lastIndex = 0;
      return detectURLRegex.test(node.textContent);
    }

    return false;
  });

  return nodesURL;
}

/**
 * Converts a Map of HTML attributes into a space-separated string of HTML attribute declarations.
 *
 * @param {Map<string, string|boolean|number>} map - A Map containing HTML attribute key-value pairs
 * @returns {string} A string of HTML attributes in the format 'key1="value1" key2="value2"'
 *
 * @example
 * const attrs = new Map([
 *   ['class', 'btn-primary'],
 *   ['disabled', true],
 *   ['data-testid', 'submit-button'],
 *   ['aria-label', 'Submit form'],
 *   ['tabindex', 0]
 * ]);
 *
 * const result = getAttributesString(attrs);
 * // Returns: 'class="btn-primary" disabled data-testid="submit-button" aria-label="Submit form" tabindex="0"'
 */
export const getAttributesString = (map: Map<string, string | boolean | number>): string =>
  Array.from(map.entries())
    .map(([key, value]: any) => {
      // Handle boolean attributes
      if (typeof value === 'boolean') {
        return value ? key : '';
      }
      // Handle other attributes
      return `${key}="${value}"`;
    })
    .filter(Boolean)
    .join(' ');

/**
 * Checks if the anchor node of the current text selection
 * is a child of a specific DOM element.
 *
 * @param {HTMLElement} element - The parent element to check against
 * @param {HTMLElement} anchorNodeInstance - The anchor node of the Tagify instance
 * @returns {boolean} True if the anchor node is a child of the element, false otherwise
 */
export const isAnchorNodeChildOfElement = (
  element: Node,
  anchorNodeInstance: Node | null,
): boolean => {
  // Get the current selection
  const selection = window.getSelection();

  // Check if there's an active selection
  if (!selection || selection.rangeCount === 0) {
    return false;
  }

  // Get the anchor node (where the selection starts)
  const { anchorNode } = selection;

  // Check if the anchor node exists and the element exists
  if (!anchorNode || !element) {
    return false;
  }

  // Use Node.contains() to check if the element contains the anchor node
  return element.contains(anchorNode) && element.contains(anchorNodeInstance);
};

export function isCaretAtEndOfTextNodeWithNextTag(range: Range) {
  const { startContainer, startOffset } = range;

  // Check if caret is inside a text node
  if (startContainer.nodeType !== Node.TEXT_NODE) return false;

  const textNode = startContainer as Node & { length: number };

  // Check if caret is at the end of the text node
  if (startOffset !== textNode.length) return false;

  // Now check if the next sibling is a tag (element node)
  let { nextSibling } = textNode;

  if (!nextSibling) return false;

  // If the next sibling is a text node, keep moving forward until we find a non-empty text node
  while (
    nextSibling.nodeType === Node.TEXT_NODE &&
    !nextSibling.textContent &&
    nextSibling.nextSibling
  ) {
    nextSibling = nextSibling.nextSibling;
  }

  return nextSibling.nodeType === Node.ELEMENT_NODE && nextSibling.nodeName === 'TAG';
}

export const getCurrentSelectionAndCloneRange = () => {
  const selection = window.getSelection();
  if (!selection?.rangeCount) return { selection, range: null };

  const range = selection.getRangeAt(0).cloneRange();
  return { selection, range };
};

function isCaretAtStartOfLineBeforeBackspace(range: Range) {
  // Clone range to avoid modifying original
  const { startContainer, startOffset } = range;

  if (startContainer.nodeType !== Node.TEXT_NODE) return false;
  const { textContent } = startContainer;
  if (typeof textContent !== 'string') return false;
  if (textContent.length === 1) return true;

  // Look backwards from caret position for newline
  const offset = 2; // Number of characters to look back, because of caret before delete by Backspace
  for (let i = startOffset - offset; i >= 0; i--) {
    const char = textContent.charAt(i);

    if (char === '\n') {
      // Found newline, check if only whitespace between newline and caret
      const textBetween = textContent.substring(i + offset, startOffset);
      return textBetween.trim() === '';
    }

    if (char.trim() !== '') {
      // Found non-whitespace character, not at line start
      return false;
    }
  }

  return false;
}

const isValidTextNodeForBackspace = (node: Node, range: Range) =>
  node.textContent &&
  node.nodeType === Node.TEXT_NODE &&
  node.textContent.length &&
  isCaretAtStartOfLineBeforeBackspace(range);

const insertZeroWidthSpaceBeforeTag = (node: Node) => {
  const { parentNode: tagifyInput, nextSibling: tagifyTag } = node;
  if (tagifyInput?.nodeName !== 'SPAN' || tagifyTag?.nodeName !== 'TAG') return;

  const textNode = document.createTextNode('\u200B');
  selectRange(tagifyInput.insertBefore(textNode, tagifyTag), 0, 0);
};

export const handleTextNodeBackspace = (range: Range) => {
  const currentNode = range.endContainer;
  if (!isValidTextNodeForBackspace(currentNode, range)) return;

  insertZeroWidthSpaceBeforeTag(currentNode);
};

export const handleEnterWithNextTag = (range: Range) => {
  const currentNode: Node = range.endContainer;
  if (currentNode.nodeType !== Node.TEXT_NODE) return;

  insertZeroWidthSpaceBeforeTag(currentNode);
};
