/* eslint-disable no-cond-assign */
/* eslint-disable no-console */
// Libraries
import { has, isUndefined } from 'lodash';
import { translate, translations } from '@antscorp/antsomi-locales';

// Utils
import { errorWrapper } from './errorWrapper';
import { isViberEmoji } from './iconsViber';
import { random } from '@antscorp/antsomi-ui/es/utils';

// Constants
import {
  CONTENT_SOURCE_GROUP,
  DATA_ACCESS_OBJECT,
  EMOJI,
  EMOJI_COLLECTIONS,
  LINE_EMOJI_PTN,
  PERSONALIZE_PTN,
  PREFIX_PATTERN_LINE_MESSAGE,
  PROMOTION_CODE,
  SHORT_LINK,
  SHORT_LINK_GENERAL_PTN,
  SHORT_LINK_INDIVIDUAL_PTN,
  SHORT_LINK_PTN,
  SHORT_LINK_TYPE,
  SHORT_LINK_V2,
  SHORT_LINK_V2_GENERAL_PTN,
  SHORT_LINK_V2_INDIVIDUAL_PTN,
  VIBER_EMOJI_PTN,
} from './constants';

// Types
import type {
  AcceptablePattern,
  MapAttributesProps,
  PatterTagName,
  PatternHandler,
  PatternHandlerWrapper,
  ShortLinkType,
  TagDataCustomize,
  TagDataEmoji,
  TagDataShortLink,
  TagDataText,
  TagType,
  TagifyInputProps,
  URLOptions,
} from './types';

interface TagInfo {
  label: string;
  type: string;
  isValid: boolean;
  message?: string;
  isRemoved?: boolean;
  hasViewPermission?: boolean;
}

/*
 * Usage to cache compiled regular expressions:
 * const regex = getCachedRegex(pattern, flags);
 */
const regexCache: Map<string, RegExp> = new Map();

/**
 * Retrieves a cached regular expression or compiles and caches it if not found.
 * @param pattern The regex pattern string.
 * @param flags Optional regex flags (e.g., 'g', 'i').
 * @param cacheKeyPrefix Optional prefix for the cache key.
 * @returns The cached or newly compiled RegExp object.
 */
export function getCachedRegex(
  pattern: string,
  flags: string = 'g',
  cacheKeyPrefix?: string,
): RegExp {
  const cacheKey = `${cacheKeyPrefix ?? pattern}-${flags}`;

  // Check if the regex is already cached
  if (regexCache.has(cacheKey)) {
    return regexCache.get(cacheKey)!; // Return the cached regex
  }

  // Compile and cache the regex
  const compiledRegex = new RegExp(pattern, flags);
  regexCache.set(cacheKey, compiledRegex);

  return compiledRegex;
}

export const detectURLRegex =
  /https:\/\/(?:www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_+.~#?&/=]*)?/gi;
export const tagRegexStringPattern = '\\[\\[({.*?})\\]\\]';

export function acceptablePatternChecking(
  pattern: AcceptablePattern,
  acceptablePattern: Array<AcceptablePattern>,
): boolean {
  return acceptablePattern.includes(pattern);
}

// Helper function to validate URL formats
/**
 * Validates a URL based on customizable options.
 *
 * @param {string} url - The URL to validate.
 * @param {URLOptions} [options={}] - An object containing various validation options.
 *
 * @param {string[]} [options.protocols=['http:', 'https:']] - An array of allowed protocols (e.g., 'http:', 'https:').
 * @param {boolean} [options.requireTld=true] - Whether a top-level domain (TLD) is required (e.g., ".com").
 * @param {boolean} [options.allowUnderscores=false] - Whether underscores are allowed in domain name segments.
 * @param {boolean} [options.allowTrailingDot=false] - Whether the domain can have a trailing dot (e.g., "example.com.").
 * @param {boolean} [options.allowNumericTld=false] - Whether numeric TLDs (e.g., ".123") are allowed.
 * @param {boolean} [options.allowIp=true] - Whether the hostname can be an IP address (IPv4 or IPv6).
 * @param {number} [options.maxLength=2083] - The maximum allowed length for the URL.
 * @param {number} [options.minLength=3] - The minimum allowed length for the URL.
 * @param {boolean} [options.requireValidPath=false] - Whether to require the URL to have a valid path (e.g., "/path").
 * @param {boolean} [options.allowDataUrl=false] - Whether Data URLs (e.g., "data:image/png;base64,...") are allowed.
 * @param {boolean} [options.allowFragment=true] - Whether fragments (e.g., "#section") are allowed in the URL.
 * @param {boolean} [options.requireFragment=false] - Whether a fragment is required in the URL.
 * @param {RegExp} [options.fragmentRegex=/^[a-z0-9\-._~!$&'()*+,;=:@/?]*$/i] - A custom regex to validate the fragment part.
 *
 * @returns {boolean} - Returns `true` if the URL is valid based on the provided options, otherwise `false`.
 *
 * @example
 * // Example 1: Validate a basic URL with default options
 * const result = isValidUrl('https://example.com');
 * console.log(result); // true
 *
 * @example
 * // Example 2: Validate a URL with a fragment, allowing only alphanumeric fragments
 * const options = {
 *   allowFragment: true,
 *   fragmentRegex: /^[a-zA-Z0-9_]*$/ // Allow only alphanumeric characters and underscores
 * };
 * const result = isValidUrl('https://example.com/#valid_fragment', options);
 * console.log(result); // true
 *
 * @example
 * // Example 3: Validate a URL requiring a fragment and allowing IP addresses
 * const options = {
 *   requireFragment: true,
 *   allowIp: true
 * };
 * const result = isValidUrl('https://***********/#section', options);
 * console.log(result); // true
 *
 * @example
 * // Example 4: Validate a Data URL
 * const options = { allowDataUrl: true };
 * const result = isValidUrl('data:image/png;base64,iVBORw0KGgoAAAANSUhEUg...', options);
 * console.log(result); // true
 */
function isValidUrl(url: string, options: URLOptions = {}): boolean {
  const {
    protocols = ['http:', 'https:'],
    requireTld = true,
    allowUnderscores = false,
    allowTrailingDot = false,
    allowNumericTld = false,
    allowIp = true,
    maxLength = 2083,
    minLength = 3,
    requireValidPath = false,
    allowDataUrl = false,
    allowFragment = true, // Default: allow fragments
    requireFragment = false, // Default: do not require fragments
    fragmentRegex = /^[a-z0-9\-._~!$&'()*+,;=:@/?]*$/i, // Default: basic URL-safe characters
  } = options;

  // Early check for non-string input
  if (typeof url !== 'string') {
    return false;
  }

  // Check length of the URL
  if (url.length < minLength || url.length > maxLength) {
    return false;
  }

  // Data URL check
  if (allowDataUrl && url.startsWith('data:')) {
    return /^data:([a-z]+\/[a-z0-9-+.]+)?;base64,([a-z0-9!$&',()*+;=\-._~:@/?%\s]*?)+$/i.test(url);
  }

  try {
    const parsedUrl = new URL(url);

    // Protocol check
    if (!protocols.includes(parsedUrl.protocol)) {
      return false;
    }

    // Host check
    if (!parsedUrl.hostname) {
      return false;
    }

    // IP address check
    const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
    const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    const isIp = ipv4Regex.test(parsedUrl.hostname) || ipv6Regex.test(parsedUrl.hostname);

    if (isIp) {
      if (!allowIp) return false;
    } else {
      // Domain name check
      const domainSegments = parsedUrl.hostname.split('.');
      if (requireTld && domainSegments.length < 2) {
        return false;
      }

      const tld = domainSegments[domainSegments.length - 1];
      if (!allowNumericTld && /^\d+$/.test(tld)) {
        return false;
      }

      const domainRegex = allowUnderscores
        ? /^[a-z0-9_]+(-[a-z0-9_]+)*$/i
        : /^[a-z0-9]+(-[a-z0-9]+)*$/i;

      for (let i = 0; i < domainSegments.length; i++) {
        const segment = domainSegments[i];
        if (segment.length > 63 || (i === domainSegments.length - 1 && segment.length < 2)) {
          return false;
        }
        if (!domainRegex.test(segment)) {
          if (!(allowTrailingDot && i === domainSegments.length - 1 && segment === '')) {
            return false;
          }
        }
      }
    }

    // Path validation
    if (requireValidPath && parsedUrl.pathname !== '/') {
      const pathRegex = /^(\/[\w\-.~!$&'()*+,;=:@%]+)+\/?$/;
      if (!pathRegex.test(parsedUrl.pathname)) {
        return false;
      }
    }

    // Port number validation
    if (parsedUrl.port) {
      const port = parseInt(parsedUrl.port, 10);
      if (isNaN(port) || port <= 0 || port > 65535) {
        return false;
      }
    }

    // Fragment validation
    if (parsedUrl.hash) {
      const fragment = parsedUrl.hash.slice(1); // Remove the leading "#"

      if (!allowFragment) {
        return false; // Fragments are not allowed
      }

      if (!fragmentRegex.test(fragment)) {
        return false; // Fragment does not match the custom fragment regex
      }
    } else {
      if (requireFragment) {
        return false; // A fragment is required but missing
      }
    }

    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Retrieves the label for a content source tag.
 * @param {string} attributeName - The attribute name from the original tag.
 * @param {string} subAttributeName - The sub-attribute name from the original tag.
 * @param {MapAttributesProps} mapAttributes - The mapping object for attributes.
 * @returns {string} The label for the content source tag.
 */
const getContentSourceLabel = (
  attributeName: string,
  subAttributeName: string,
  mapAttributes: MapAttributesProps,
): string => {
  const csRegex = getCachedRegex('^([^\\[]+)', 'g', 'cs_group_Regex');
  const match = attributeName.match(csRegex);

  if (match) {
    const csGroupId = match[0];
    const mapping = mapAttributes[csGroupId];

    return (
      mapping?.[subAttributeName]?.display || mapping?.[subAttributeName]?.label || subAttributeName
    );
  }

  return attributeName;
};

/**
 * Retrieves the label for a attribute tag.
 * @param {string} type - The type from the original tag.
 * @param {string} attributeName - The attribute name from the original tag.
 * @param {string | undefined} subAttributeName - The sub-attribute name from the original tag, if any.
 * @param {MapAttributesProps} mapAttributes - The mapping object for attributes.
 * @returns {string} The label for the attribute tag.
 */
const getLabelAttribute = (
  type: string,
  attributeName: string,
  subAttributeName: string | undefined,
  mapAttributes: MapAttributesProps,
): string => {
  const mapping = mapAttributes[type];

  if (!mapping) return type;

  if (mapping[attributeName]) {
    return (
      mapping[attributeName]?.label ||
      mapping[attributeName]?.display ||
      mapping[attributeName]?.template_name ||
      mapping[attributeName]?.value ||
      type
    );
  }

  const combineAttribute = `${attributeName}.${subAttributeName}`;

  return (
    mapping[combineAttribute]?.label ||
    mapping[combineAttribute]?.display ||
    mapping[combineAttribute]?.template_name ||
    combineAttribute
  );
};

export const getPersonalizeTagInfo = (
  originalTag: string,
  mapAttributes?: MapAttributesProps,
  mapErrorAttributes?: MapAttributesProps,
): TagInfo => {
  try {
    const [type, attributeName, subAttributeName] = originalTag.split('.');
    const isCsGroup = type === CONTENT_SOURCE_GROUP;

    if (!mapAttributes || (!mapAttributes[type] && !isCsGroup)) {
      // return fallback if no mapping
      return { type, label: attributeName || type, isValid: false, isRemoved: false };
    }

    // Get label based on mapping
    const label = isCsGroup
      ? getContentSourceLabel(attributeName, subAttributeName, mapAttributes)
      : getLabelAttribute(type, attributeName, subAttributeName, mapAttributes);

    // Check additional case for each tag type
    let [isValid, message, isRemoved, hasViewPermission] = [true, '', false, true];
    switch (type) {
      case PROMOTION_CODE: {
        // Check if the code is exist in the map
        if (mapAttributes[type] && attributeName) {
          const isExistPromotionPool = has(mapAttributes[type], attributeName);

          if (!isExistPromotionPool) {
            isValid = false;
            message = 'This pool does not exist anymore';

            if (
              mapErrorAttributes &&
              mapErrorAttributes[type] &&
              mapErrorAttributes[type][attributeName]
            ) {
              const {
                isEdit = false,
                isView = false,
                isExist = true,
              } = mapErrorAttributes[type][attributeName] || {};
              // Case Pool removed
              if (!isExist) {
                isRemoved = !isExist;
                message = translate(
                  translations._PERSONALIZATION_TAG_ERR_REMOVED,
                  'This pool is removed',
                );
              } else if (!isView) {
                // Case No permission
                hasViewPermission = isView;
                message = translate(
                  translations._PERMISSION_ERR_POOL,
                  'You do not have permission on this pool',
                );
              }
            }
          }
        }

        break;
      }
      default: {
        break;
      }
    }

    return {
      type,
      message,
      isValid,
      label: label || type,
      isRemoved,
      hasViewPermission,
    };
  } catch (error) {
    console.error('Error in getTagLabel', error);
    return { label: originalTag, type: originalTag, isValid: false, isRemoved: false };
  }
};

export const getShortLinkTagInfo = (params: {
  type: string;
  label: string;
  url: string;
  shortener: string;
  mapAttributes?: MapAttributesProps;
  mapErrorAttributes?: MapAttributesProps;
}): TagInfo => {
  const { type, label, url, shortener, mapAttributes, mapErrorAttributes } = params;
  try {
    let [isValid, message] = [true, ''];

    switch (type) {
      case SHORT_LINK_V2:
        if (mapAttributes && mapAttributes[type]) {
          const isExistShortLink = has(mapAttributes[type], shortener);

          if (!isExistShortLink) {
            isValid = false;
            message = translate(
              (translations as any)?._PER_TAG_LINK_ERR || '',
              'This shortener is inactivated or removed',
            );

            if (
              mapErrorAttributes &&
              mapErrorAttributes?.[type] &&
              mapErrorAttributes?.[type]?.[shortener]
            ) {
              const { isView = true, isExist = true } = mapErrorAttributes[type][shortener] || {};
              if (!isExist || !isView) {
                isValid = false;
                message = translate(
                  (translations as any)?._PER_TAG_LINK_ERR || '',
                  'This shortener is inactivated or removed',
                );
              }
            }
          }
        }
        break;

      default:
        break;
    }
    return {
      type,
      message,
      isValid,
      label,
    };
  } catch (error) {
    console.error('Error in getTagLabel', error);
    return {
      label,
      type,
      isValid: false,
      message: translate('', 'This shortener is inactivated or removed'),
    };
  }
};

const createTagPattern = (tagInfo: TagDataCustomize): string => `[[${JSON.stringify(tagInfo)}]]`;

const generateShortlinkLabel = (params: { type: ShortLinkType }) => {
  const { type } = params;

  switch (type) {
    case SHORT_LINK_TYPE.INDIVIDUAL: {
      return 'Individual Shortlink';
    }
    case SHORT_LINK_TYPE.GENERAL: {
      return 'General Shortlink';
    }

    // case SHORT_LINK_TYPE_V2.INDIVIDUAL: {
    //   return 'Individual Shortlink V2';
    // }
    // case SHORT_LINK_TYPE_V2.GENERAL: {
    //   return 'General Shortlink V2';
    // }
    default: {
      const randomId = random(5);

      return `https://ants.ly/${randomId}`;
    }
  }
};

const PERSONALIZE_PTN_PATTERN = '#\\{(?!shortlink|line|viber)([^}]+?)\\}' as const;

/*
 * Function to handle #{shortlink(...)} pattern
 *
 */
const handleShortlinkIndividualPattern: PatternHandler = match => {
  const [fullMatch] = match;

  let matchUrl: RegExpExecArray | null;
  let url: string = '';

  const urlRegex = getCachedRegex('\\(([^)]+)\\)', 'g', 'urlRegex');
  const label = generateShortlinkLabel({ type: SHORT_LINK_TYPE.INDIVIDUAL });

  while ((matchUrl = urlRegex.exec(fullMatch)) !== null) {
    [, url] = matchUrl;
  }

  const urlForValidate = url.replace(
    new RegExp(PERSONALIZE_PTN_PATTERN, 'g'),
    'personalize_pattern',
  );

  const isValid = isValidUrl(urlForValidate, {
    protocols: ['https:'],
    requireTld: false,
    allowFragment: true,
    requireFragment: false,
    allowNumericTld: true,
    allowUnderscores: true,
    allowTrailingDot: true,
    allowIp: true,
  });

  // Validate the URL inside the shortlink pattern
  if (!isValid) {
    console.error(
      `Invalid URL detected in shortlink individual: Full match: ${fullMatch} - URL: ${url} - Validate URL: ${urlForValidate}`,
    );

    return {
      isValid,
      tag: '[[Invalid URL]]',
      tagData: undefined,
    };
  }

  const tagData: TagDataShortLink = {
    label,
    type: SHORT_LINK,
    shortlinkType: SHORT_LINK_TYPE.INDIVIDUAL,
    value: fullMatch,
  };

  const tag = createTagPattern(tagData);

  return { isValid, tag, tagData };
};

/*
 * Function to handle #{shortlink_static(...)} pattern
 *
 */
const handleShortlinkGeneralPattern: PatternHandler = match => {
  const [fullMatch] = match;

  let matchUrl: RegExpExecArray | null;
  let url: string = '';

  const urlRegex = getCachedRegex('\\(([^)]+)\\)', 'g', 'urlRegex');
  const label = generateShortlinkLabel({ type: SHORT_LINK_TYPE.GENERAL });

  while ((matchUrl = urlRegex.exec(fullMatch)) !== null) {
    [, url] = matchUrl;
  }

  const isValid = isValidUrl(url, {
    protocols: ['https:'],
    requireTld: false,
    allowFragment: true,
    requireFragment: false,
    allowNumericTld: true,
    allowUnderscores: true,
    allowTrailingDot: true,
    allowIp: true,
  });

  // Validate the URL inside the shortlink general pattern
  if (!isValid) {
    console.error(`Invalid URL detected in shortlink general: fullMatch:: ${fullMatch}`);

    return {
      isValid,
      tag: '[[Invalid URL]]',
      tagData: undefined,
    };
  }

  const tagData: TagDataShortLink = {
    label,
    type: SHORT_LINK,
    shortlinkType: SHORT_LINK_TYPE.GENERAL,
    value: fullMatch,
  };

  const tag = createTagPattern(tagData);

  return { isValid, tag, tagData };
};

/*
 * Function to handle $((line:<ProductId>:<CodeImage>)) pattern
 */
const handleLinePattern: PatternHandler = match => {
  const [, lineProductId, lineCodeImage] = match;
  const lineValue = `$((${PREFIX_PATTERN_LINE_MESSAGE}:${lineProductId}:${lineCodeImage}))`;

  if (!lineProductId || !lineCodeImage) {
    console.error('Invalid line pattern detected: ', lineValue);

    return {
      isValid: false,
      tag: '[[Invalid Emoji Line]]',
      tagData: undefined,
    };
  }

  const tagData: TagDataEmoji = {
    label: lineValue,
    value: lineValue,
    type: EMOJI,
    collection: EMOJI_COLLECTIONS.LINE,
  };

  const tag = createTagPattern(tagData);

  return { isValid: true, tag, tagData };
};

/*
 * Function to handle generic of personalize #{...} pattern
 */
const handlePersonalizeTagPattern: PatternHandler = match => {
  const [personalizeTag, personalizeContent] = match;
  const [tagCode] = personalizeContent.split('||');
  const [type, attributeName, subAttributeName] = tagCode.split('.');

  if (!tagCode || !type) {
    console.error('Invalid personalize pattern detected: ', tagCode);

    return {
      isValid: false,
      tag: '[[Invalid Personalize]]',
      tagData: undefined,
    };
  }

  const isContentSourceTag = type === CONTENT_SOURCE_GROUP;
  const label = isContentSourceTag && subAttributeName ? subAttributeName : attributeName || type;

  const tagData: TagDataText = {
    label,
    type: type as TagType,
    value: personalizeTag,
    attributeName,
  };

  const tag = createTagPattern(tagData);

  return { isValid: true, tag, tagData };
};

/*
 * Function to handle Viber (...) pattern
 * Usage for example: Viber Emoji: (smiley)
 */
const handleViberPattern: PatternHandler = match => {
  const [, viberContent] = match;

  const isValid = !!viberContent && isViberEmoji(viberContent);

  if (!isValid) {
    // console.error('Invalid viber pattern detected: ', viberContent);

    return {
      isValid: false,
      tag: '[[Invalid Viber]]',
      tagData: undefined,
    };
  }

  const tagData: TagDataEmoji = {
    type: EMOJI,
    value: `(${viberContent})`,
    label: viberContent,
    collection: EMOJI_COLLECTIONS.VIBER,
  };

  const tag = createTagPattern(tagData);

  return { isValid, tag, tagData };
};

/*
 * Function to handle #{shortlink_v2(...)} pattern
 *
 */
const handleShortlinkV2IndividualPattern: PatternHandler = match => {
  const [fullMatch, type, shortener, url = ''] = match;

  const label = generateShortlinkLabel({ type: SHORT_LINK_TYPE.INDIVIDUAL });

  const urlForValidate = url?.replace(
    new RegExp(PERSONALIZE_PTN_PATTERN, 'g'),
    'personalize_pattern',
  );

  const isValid = isValidUrl(urlForValidate, {
    protocols: ['https:'],
    requireTld: false,
    allowFragment: true,
    requireFragment: false,
    allowNumericTld: true,
    allowUnderscores: true,
    allowTrailingDot: true,
    allowIp: true,
  });

  // Validate the URL inside the shortlink pattern
  if (!isValid) {
    console.error(
      `Invalid URL detected in shortlink individual: Full match: ${fullMatch} - URL: ${url} - Validate URL: ${urlForValidate}`,
    );

    return {
      isValid,
      tag: '[[Invalid URL]]',
      tagData: undefined,
    };
  }

  const tagData: TagDataShortLink = {
    label,
    type: SHORT_LINK_V2,
    shortlinkType: SHORT_LINK_TYPE.INDIVIDUAL,
    value: fullMatch,
    shortener,
    url,
  };

  const tag = createTagPattern(tagData);

  return { isValid, tag, tagData };
};

/*
 * Function to handle #{shortlink_v2_static(...)} pattern
 *
 */
const handleShortlinkV2GeneralPattern: PatternHandler = match => {
  const [fullMatch, type, shortener, url] = match;

  const label = generateShortlinkLabel({ type: SHORT_LINK_TYPE.GENERAL });

  const isValid = isValidUrl(url, {
    protocols: ['https:'],
    requireTld: false,
    allowFragment: true,
    requireFragment: false,
    allowNumericTld: true,
    allowUnderscores: true,
    allowTrailingDot: true,
    allowIp: true,
  });

  // Validate the URL inside the shortlink general pattern
  if (!isValid) {
    console.error(`Invalid URL detected in shortlink general: fullMatch:: ${fullMatch}`);

    return {
      isValid,
      tag: '[[Invalid URL]]',
      tagData: undefined,
    };
  }

  const tagData: TagDataShortLink = {
    label,
    type: SHORT_LINK_V2,
    shortlinkType: SHORT_LINK_TYPE.GENERAL,
    value: fullMatch,
    shortener,
    url,
  };

  const tag = createTagPattern(tagData);

  return { isValid, tag, tagData };
};

// Map of regex patterns to their respective wrapped handlers
export const patternHandlers: Record<PatterTagName, PatternHandlerWrapper> = {
  /*
   * NOTE: Handle shortlink patterns with improved specificity, preventing false positives.
   */
  [SHORT_LINK_INDIVIDUAL_PTN]: {
    pattern: '#\\{shortlink\\([^()]+?\\)\\}(?!_static)',
    name: SHORT_LINK_INDIVIDUAL_PTN, // Used to cache the regex, please keep the name unique for each pattern
    acceptablePattern: SHORT_LINK_PTN,
    handler: errorWrapper(handleShortlinkIndividualPattern),
  },

  /*
   * NOTE: Static shortlink patterns, made specific to static to avoid overlaps with general shortlink patterns.
   */
  [SHORT_LINK_GENERAL_PTN]: {
    pattern: '#\\{shortlink_static\\([^()]+?\\)\\}',
    name: SHORT_LINK_GENERAL_PTN, // Used to cache the regex, please keep the name unique for each pattern
    acceptablePattern: SHORT_LINK_PTN,
    handler: errorWrapper(handleShortlinkGeneralPattern),
  },

  [SHORT_LINK_V2_INDIVIDUAL_PTN]: {
    pattern: '#\\{(shortlink_v2)\\(([^|]+)\\|\\s*([^)]+)\\)\\}',
    // pattern: '#\\{shortlink_v2\\([^()]+?\\)\\}(?!_static)',
    name: SHORT_LINK_V2_INDIVIDUAL_PTN, // Used to cache the regex, please keep the name unique for each pattern
    acceptablePattern: SHORT_LINK_PTN,
    handler: errorWrapper(handleShortlinkV2IndividualPattern),
  },
  [SHORT_LINK_V2_GENERAL_PTN]: {
    pattern: '#\\{(shortlink_static_v2)\\(([^|]+)\\|\\s*([^)]+)\\)\\}',
    name: SHORT_LINK_V2_GENERAL_PTN, // Used to cache the regex, please keep the name unique for each pattern
    acceptablePattern: SHORT_LINK_PTN,
    handler: errorWrapper(handleShortlinkV2GeneralPattern),
  },

  /*
   * NOTE: Line pattern with strict boundaries to ensure it matches only the intended format.
   */
  [LINE_EMOJI_PTN]: {
    pattern: '\\$\\(\\(line:([^:]+?):(\\d+)\\)\\)',
    name: LINE_EMOJI_PTN, // Used to cache the regex, please keep the name unique for each pattern
    acceptablePattern: LINE_EMOJI_PTN,
    handler: errorWrapper(handleLinePattern),
  },

  // NOTE: Personalize patterns now include lookahead to ensure they do not match any known pattern like shortlink.
  [PERSONALIZE_PTN]: {
    pattern: PERSONALIZE_PTN_PATTERN,
    name: PERSONALIZE_PTN, // Used to cache the regex, please keep the name unique for each pattern
    acceptablePattern: PERSONALIZE_PTN,
    handler: errorWrapper(handlePersonalizeTagPattern),
  },

  /*
   * NOTE: Viber pattern: Excludes URLs, shortlinks, and line-like formats using negative lookaheads.
   * Avoids matching anything starting with `http`, `https`, or `www` to prevent capturing URLs.
   */
  [VIBER_EMOJI_PTN]: {
    pattern: '\\((?!line:|shortlink|https?:|www\\.|\\$\\(\\(line:|shortlink_static)([^()]+?)\\)',
    name: VIBER_EMOJI_PTN, // Used to cache the regex, please keep the name unique for each pattern
    acceptablePattern: VIBER_EMOJI_PTN,
    handler: errorWrapper(handleViberPattern),
  },
};
