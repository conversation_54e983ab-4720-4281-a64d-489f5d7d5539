import { ReactNode } from 'react';
import { MODE } from './config';

export type Mode = (typeof MODE)[keyof typeof MODE];

export type ItemKey = string;

export type ItemType = {
  key: ItemKey;
  className?: string;
  scrollingLabel?: string;
  label?: ReactNode;
  children?: ItemType[];
  errorMessage?: string | undefined;
  disabled?: boolean;
};

export type SerializedItemType = ItemType & {
  level: number;
  pathKey: PathKey;
  parent: ItemType | null;
};

type VirtualizedMenuBaseProps = Partial<{
  items: ItemType[];
  inlineIndent: number;
  inlinePadding: number;
  itemSpacing: number;
  selectable: boolean;
  className: string;
  itemSize: number;
  selected: string[];
  expanded: string[];
  onClick: (args: { item: ItemType; pathKey: PathKey }) => void;
  action: React.ReactNode | ((item: ItemType) => React.ReactNode);
}>;

export type VirtualizedMenuBaseHandle = {
  expandAll: () => void;
};

export type PathKey = (string | number)[];

// Menu inline
export type MenuInlineHandle = VirtualizedMenuBaseHandle;

export type MenuInlineProps = VirtualizedMenuBaseProps & {
  mode?: typeof MODE.Inline;
};

// Union
export type VirtualizedMenuHandle = MenuInlineHandle;

export type VirtualizedMenuProps = MenuInlineProps;
