import React, { memo } from 'react';
import { Typography } from '../../../../atoms/Typography';
import { ItemType, MenuInlineProps, SerializedItemType } from '../../types';
import { MenuItemRoot } from '../../styled';
import clsx from 'clsx';
import { ListChildComponentProps, areEqual } from 'react-window';
import { isEmpty } from 'lodash';
import { CLS } from '../../utils';
import { ExpandMoreIcon } from '@antscorp/antsomi-ui/es/components/icons';
import { globalToken } from '@antscorp/antsomi-ui/es/constants';

export type MemoryData = {
  itemSpacing: number;
  inlineIndent: number;
  inlinePadding: number;
  items: SerializedItemType[];
  selectedKeys: Set<string>;
  expandedKeys: Set<string>;
  action: MenuInlineProps['action'];
  onClick: (item: ItemType, index: number) => void;
};

export const Item = memo((props: ListChildComponentProps<MemoryData>) => {
  const { style = {}, data, index = 0 } = props;

  const {
    inlineIndent,
    itemSpacing,
    inlinePadding,
    items,
    selectedKeys,
    expandedKeys,
    action,
    onClick,
  } = data || {};

  const item = items[index];

  const indentSize = ((item.level || 0) - 1) * inlineIndent;

  const itemStyle: React.CSSProperties = {
    ...style,
    paddingLeft: indentSize + inlinePadding,
    paddingRight: inlinePadding,
    top: index === 0 ? Number(style.top) : Number(style.top) + itemSpacing,
    height: index === 0 ? Number(style.height) : Number(style.height) - itemSpacing,
  };

  const itemClassName = clsx(
    CLS.Item.default,
    {
      [CLS.Item.selected]: selectedKeys.has(item.key),
      [CLS.Item.expanded]: expandedKeys.has(item.key),
      [CLS.Item.disabled]: item?.disabled,
    },
    item.className,
  );

  const handleOnClick = () => {
    if (!onClick || item?.disabled) return;

    onClick(item, index);
  };

  const renderLabel = () => {
    if (typeof item?.label === 'string') {
      return <Typography.Text ellipsis={{ tooltip: true }}>{item.label}</Typography.Text>;
    }

    return item?.label;
  };

  const renderAction = () => {
    if (action) {
      return (
        <div className={CLS.ItemAction.default}>
          {typeof action === 'function' ? action(item) : action}
        </div>
      );
    }

    if (!isEmpty(item.children)) {
      return (
        <div className={CLS.ItemAction.default}>
          <ExpandMoreIcon
            color={globalToken?.colorIcon}
            size={20}
            className={CLS.IconExpand.default}
          />
        </div>
      );
    }

    return null;
  };

  return (
    <MenuItemRoot className={itemClassName} style={itemStyle} onClick={handleOnClick}>
      <div className={CLS.ItemLabel.default}>{renderLabel()}</div>

      {renderAction()}
    </MenuItemRoot>
  );
}, areEqual);
