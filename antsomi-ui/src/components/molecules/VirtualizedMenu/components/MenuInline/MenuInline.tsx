import React, {
  ComponentPropsWithoutRef,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import { StyledAutoSizer, VirtualizedMenuContainer } from '../../styled';
import { INLINE_INDENT, INLINE_PADDING, ITEM_SIZE, ITEM_SPACING } from '../../config';
import { ItemType, MenuInlineHandle, MenuInlineProps, SerializedItemType } from '../../types';
import { CLS, getFlattenItems, getItemSize } from '../../utils';
import { Item } from '../Item';
import { useDeepCompareEffect } from '@antscorp/antsomi-ui/es/hooks';
import { MemoryData } from '../Item/Item';
import clsx from 'clsx';

const innerElementType = forwardRef<HTMLDivElement, ComponentPropsWithoutRef<'div'>>(
  ({ style, ...rest }, ref) => <div ref={ref} style={style} {...rest} />,
);

const MenuInlineInner = (props: MenuInlineProps, ref: React.ForwardedRef<MenuInlineHandle>) => {
  const {
    items: itemsProp = [],
    inlineIndent = INLINE_INDENT,
    inlinePadding = INLINE_PADDING,
    itemSize = ITEM_SIZE,
    itemSpacing = ITEM_SPACING,
    selectable = false,
    expanded: expandedProp = [],
    selected: selectedProp = [],
    action,
    className,
    onClick,
  } = props;

  const [expandedKeys, setExpandedKeys] = useState<Set<string>>(new Set());
  const [selectedKeys, setSelectedKeys] = useState<Set<string>>(new Set());
  const [flattenItems, setFlattenItems] = useState<SerializedItemType[]>([]);

  useDeepCompareEffect(() => {
    setSelectedKeys(new Set(selectedProp));
  }, [selectedProp]);

  useDeepCompareEffect(() => {
    setExpandedKeys(new Set(expandedProp));
  }, [expandedProp]);

  useEffect(() => {
    setFlattenItems(getFlattenItems({ items: itemsProp }));
  }, [itemsProp]);

  const visibleItems = flattenItems.filter(item => {
    const isRootItem = item.level === 1;
    const isParentExpand = item.parent && expandedKeys.has(String(item.parent.key));

    return isRootItem || isParentExpand;
  });

  const handleCollapseItems = (items: ItemType[]) => {
    const newOpenKeys = new Set(expandedKeys);

    getFlattenItems({ items }).forEach(i => {
      const { key } = i;

      if (expandedKeys.has(key)) {
        newOpenKeys.delete(key);
      }
    });

    setExpandedKeys(newOpenKeys);
  };

  const handleExpandItems = (items: ItemType[]) => {
    const updatedExpandedKeys = new Set(expandedKeys);

    items.forEach(item => {
      if (!item.children?.length) return;

      updatedExpandedKeys.add(item.key);
    });

    setExpandedKeys(updatedExpandedKeys);
  };

  useImperativeHandle(ref, () => ({
    expandAll: () => {
      handleExpandItems(flattenItems);
    },
  }));

  const handleClickItem = (item: ItemType, idx: number) => {
    const { key } = item;

    const isOpen = expandedKeys.has(key);

    const hasChilren = !!item.children?.length;

    if (hasChilren && isOpen) {
      handleCollapseItems([item]);
    }

    if (hasChilren && !isOpen) {
      handleExpandItems([item]);
    }

    if (selectable && !hasChilren && !selectedKeys.has(key)) {
      setSelectedKeys(new Set([item.key]));
    }

    onClick?.({
      item,
      pathKey: visibleItems[idx].pathKey,
    });
  };

  const itemData: MemoryData = {
    itemSpacing,
    inlineIndent,
    inlinePadding,
    selectedKeys,
    expandedKeys,
    action,
    items: visibleItems,
    onClick: handleClickItem,
  };

  return (
    <StyledAutoSizer className={clsx(CLS.Root.default, className)}>
      {autoSize => (
        <VirtualizedMenuContainer
          className={clsx(CLS.MenuInline.default)}
          itemSize={getItemSize(itemSize, itemSpacing)}
          itemCount={visibleItems.length}
          height={autoSize.height}
          width={autoSize.width}
          innerElementType={innerElementType}
          itemData={itemData}
          useIsScrolling
        >
          {Item}
        </VirtualizedMenuContainer>
      )}
    </StyledAutoSizer>
  );
};

export const MenuInline = React.forwardRef(MenuInlineInner);
