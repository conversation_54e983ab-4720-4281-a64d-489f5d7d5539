import { Meta, StoryObj } from '@storybook/react';
import { VirtualizedMenu } from '.';

import { LIST_ITEMS } from './__mocks__';
import { Flex } from 'antd';
import { ResizableBox } from 'react-resizable';
import { useRef } from 'react';
import { MenuInlineHandle } from './types';

const meta = {
  title: 'Molecules/VirtualizedMenu',
  component: VirtualizedMenu,
} satisfies Meta<typeof VirtualizedMenu>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default = {
  args: {
    items: LIST_ITEMS,
    itemSize: 50,
  },
  render: args => {
    const ref = useRef<MenuInlineHandle>(null);

    return (
      <>
        <button type="button" onClick={() => ref.current?.expandAll()}>
          Expand All
        </button>

        <Flex vertical style={{ height: 500 }}>
          <div style={{ height: 200, width: 100, backgroundColor: 'yellow' }}>Another Content</div>
          <VirtualizedMenu {...args} ref={ref} />
        </Flex>
      </>
    );
  },
} satisfies Story;

export const AutoSize = {
  args: {
    items: LIST_ITEMS,
  },
  render: args => (
    <ResizableBox
      width={400}
      height={500}
      resizeHandles={['sw', 'se', 'nw', 'ne', 'w', 'e', 'n', 's']}
    >
      <VirtualizedMenu {...args} />
    </ResizableBox>
  ),
} satisfies Story;
