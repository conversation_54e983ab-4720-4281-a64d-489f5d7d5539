import { ItemType } from '../types';

export const LIST_ITEMS: ItemType[] = [
  {
    key: 'bo',
    label: 'Business Objects',
    children: [
      {
        key: 'fe_test_app_defined',
        label: '[FE] Test app defined',
        disabled: true,
      },
      {
        key: 'lead',
        label: 'lead',
        className: 'test-class-name',
      },
      {
        key: 'promotion_code',
        label: 'Promotion Code',
      },
      {
        key: 'insight_property',
        label: 'Insight Property',
      },
      {
        key: 'test_11111111111111111111',
        label: 'test 11111111111111111111',
      },
      {
        key: 'event_category',
        label: 'Event Category',
      },
      {
        key: 'os',
        label: 'Operating System',
      },
      {
        key: 'province',
        label: 'Province',
      },
      {
        key: 'thangbt_test_object_412_1',
        label:
          'thangbt test object 4/12 #1 thangbt test object 4/12 #1 thangbt test object 4/12 #1 thangbt test object 4/12 #1',
      },
      {
        key: 'thangbt_test_object_2811_0',
        label: 'thangbt test object 28/11 #0',
      },
      {
        key: 'thanh_test_create_object_',
        label: 'thanh test create object ',
      },
      {
        key: 'thanh_test_create_object_01',
        label: 'thanh test create object 01',
      },
      {
        key: 'thangbt_test_object_2811_1',
        label: 'thangbt test object 28/11 #1',
      },
      {
        key: 'thangbt_test_create_obj',
        label: 'thangbt test create object 4/12#1',
      },
      {
        key: 'new__update_data_from_event_constantly',
        label: 'NEW - Update data from event constantly',
      },
      {
        key: 'phogn_dang_test',
        label: 'phogn dang test',
      },
      {
        key: 'object',
        label: 'Object Ver1',
      },
      {
        key: 'bo_check_new_import',
        label: 'BO Check new import',
      },
      {
        key: 'hoang_tets_bo',
        label: '[Hoang] Tets BO',
      },
      {
        key: 'dev_type',
        label: 'Device Type',
      },
      {
        key: 'event_action',
        label: 'Event Action',
      },
      {
        key: 'fe_phong_le2',
        label: '[FE] Phong Le2',
      },
      {
        key: 'test_computed_attributte_count_event',
        label: 'Test Computed Attributte (Count Event)',
      },
      {
        key: 'catch',
        label: 'Catch',
      },
      {
        key: 'portal',
        label: <div>Portal</div>,
      },
      {
        key: 'testt',
        label: 'Testt',
      },
      {
        key: 'duy_xem_bo_126',
        label: 'DUY XEM BO 12/6',
      },
      {
        key: 'fe_allow_to_create',
        label: '[FE] Allow to Create 23',
      },
      {
        key: 'thangbt_test_bo_612_0',
        label: 'thangbt test bo 6/12 #0',
      },
      {
        key: 'bo_d_xem_new_126',
        label: 'BO D XEM NEW 12/6',
      },
      {
        key: 'test_data_object_2',
        label: 'test data object 2',
      },
      {
        key: 'nhi_check_v_tr_2_tab',
        label: 'nhi check vị trí 2 tab',
      },
      {
        key: 'hahahha',
        label: 'hahahâha',
      },
      {
        key: 'nhi_test_11',
        label: 'nhi test 11',
      },
      {
        key: 'test_access',
        label: 'test access',
      },
      {
        key: 'nhi_test_12',
        label: 'nhi test 12',
      },
      {
        key: 'test_create_bo_2',
        label: 'test create BO 2',
      },
      {
        key: 'tets_to_mi_456721',
        label: 'tets tạo mới 456721',
      },
      {
        key: 'sad',
        label: 'SAD',
      },
      {
        key: 'test_create_bo_123',
        label: 'test create bo 123',
      },
      {
        key: 'test_107',
        label: 'Test 10/7',
      },
      {
        key: 'test_create_bo_1234',
        label: 'test create bo 1234',
      },
      {
        key: 'test_new_model',
        label: 'Test new model',
      },
      {
        key: 'testttttttttttttttttttttttt',
        label: 'testttttttttttttttttttttttt',
      },
      {
        key: 'test_feeeeeeeeeeeeeee',
        label: 'test feeeeeeeeeeeeeee',
      },
      {
        key: 'to_mi',
        label: 'Tạo mới',
      },
      {
        key: 'test_create_bo_1123',
        label: 'test create bo 1123',
      },
      {
        key: 'dear_god',
        label: 'dear god',
      },
      {
        key: 'test123',
        label: 'Test123',
      },
      {
        key: 'channel',
        label: 'Channel',
      },
      {
        key: 'test_create_for_another_user_02',
        label: 'test create for another user 02',
      },
      {
        key: 'test_event',
        label: 'Test Event',
      },
      {
        key: 'test_li_highlight_bo',
        label: 'test lại highlight BO',
      },
      {
        key: 'n_check_',
        label: 'n check ',
      },
      {
        key: 'n_test_10',
        label: 'n test 10',
      },
      {
        key: 'n_test_11',
        label: 'n test 11',
      },
      {
        key: 'to_t_account_khc_3',
        label: 'tạo từ account khác 3',
      },
      {
        key: 'videoxss',
        label: 'ALOOO',
      },
      {
        key: 'to_t_account_khc_4',
        label: 'tạo từ account khác 4',
      },
      {
        key: 'k_bo_lnkh',
        label: '[K] BO LNKH',
      },
      {
        key: 'test_fe_1511123',
        label: 'Test Fe 15/11123',
      },
      {
        key: 'videoxsssss',
        label: 'Videoxss',
      },
      {
        key: 'n_test_12',
        label: 'n test 12',
      },
      {
        key: 'variant',
        label: 'Variant',
      },
      {
        key: 'fe_test_1047',
        label: 'Fe test 10:47',
      },
      {
        key: 'test_create_bo_01',
        label: 'test create BO 01',
      },
      {
        key: 'boys_over_flowers',
        label: 'BOYS OVER FLOWERS.',
      },
      {
        key: 'test_li_popup_ny',
        label: 'test lại popup này',
      },
      {
        key: 'purchase',
        label: 'Purchase',
      },
      {
        key: 'thangbt_test_object_412_2',
        label: 'thangbt test object 4/12 #2',
      },
      {
        key: 'thangbt_test_object_2811_2',
        label: 'thangbt test object 28/11 #2',
      },
      {
        key: 'members',
        label: 'MEMBERS',
      },
      {
        key: 'city',
        label: 'City',
      },
      {
        key: 'k_test_jt_object',
        label: '[K] Test JT Object',
      },
      {
        key: 'segment',
        label: 'Segment',
      },
      {
        key: 'browser',
        label: 'Browser',
      },
      {
        key: 'lethanhcong123',
        label: 'Object',
      },
      {
        key: 'hhhhhhhhhhhhhhhh',
        label: 'hhhhhhhhhhhhhhhh',
      },
      {
        key: 'k_bo_2',
        label: '[K] BO 2',
      },
      {
        key: 'n_test_data_object_1',
        label: 'n test data object 1 ',
      },
      {
        key: 'dev_1511',
        label: 'Dev 15/11',
      },
      {
        key: 'test_columns',
        label: 'test columns',
      },
      {
        key: 'fe_test_items',
        label: '[FE] Test Items 222',
      },
      {
        key: 'n_test_2',
        label: 'n test 2',
      },
      {
        key: 'demo111',
        label: 'demo111',
      },
      {
        key: 'test_123',
        label: 'test 123',
      },
      {
        key: 'eidt__none',
        label: 'eidt = none',
      },
      {
        key: 'k_test_create_bo',
        label: '[K] Test create bo',
      },
      {
        key: 'users',
        label: 'Visitor',
      },
      {
        key: 'test1',
        label: 'Test1',
      },
      {
        key: 'thangbt_test_object_412_0',
        label: 'thangbt test object 4/12 #0',
      },
      {
        key: 'n_bo_transaction_',
        label: '[N] BO Transaction ',
      },
      {
        key: 'bo_setting_update_data_from_event_constantly',
        label: 'BO SETTING Update data from event constantly',
      },
      {
        key: 'create_antsomi_product_owner',
        label: '[Create] Antsomi product Owner edit',
      },
      {
        key: 'test_permission_3',
        label: 'test permission 3',
      },
      {
        key: 'nhi_check_v_tri_2_tab_2',
        label: 'nhi check vị tri 2 tab 2',
      },
      {
        key: 'test_create_bo_3',
        label: 'test create BO 3',
      },
      {
        key: 'thien_13123',
        label: 'Thien 13123',
      },
      {
        key: 'zone',
        label: 'Geo Zone',
      },
      {
        key: 'devvvv_de',
        label: 'Devvvv De',
      },
      {
        key: 'tuan_nguyen_1311',
        label: 'Tuan Nguyen 13/11',
      },
      {
        key: 'qc_t_test_change_assign_group',
        label: 'QC T Test change assign group edit',
      },
      {
        key: 'fe_test_dat_le_1123123',
        label: 'Fe test Dat Le 1123123',
      },
      {
        key: 'test_permission_01',
        label: 'test permission 01',
      },
      {
        key: 'test_create_bo_for_another_user_01',
        label: 'test create bo for another user 01',
      },
      {
        key: 't_to_mi_',
        label: 'T tạo mới ',
      },
      {
        key: 'test_123123',
        label: 'test 123123',
      },
      {
        key: 'story',
        label: 'Journey',
      },
      {
        key: 'huytc_test_schedule',
        label: 'huytc test schedule',
      },
      {
        key: 'hghghg',
        label: 'duplicate BO code',
      },
      {
        key: 'huytx_create_object_1',
        label: '[huytx] create object 1',
      },
      {
        key: 'fe_test_dat_le',
        label: 'Fe test Dat Le',
      },
      {
        key: 'to_t_account_khc_2',
        label: 'tạo từ account khác 2',
      },
      {
        key: 'ls2000',
        label: 'LS2000',
      },
      {
        key: 'test_owner',
        label: 'test owner',
      },
      {
        key: 'create_li_g_u',
        label: 'create lỗi gì đâu',
      },
      {
        key: 'n_test_7',
        label: 'n test 7',
      },
      {
        key: 't_create_bo_lan_nnnnn',
        label: '[T] CREATE BO LAN NNNNN',
      },
      {
        key: 'core_test_bo',
        label: 'core_test_bo',
      },
      {
        key: 'bo_test_test',
        label: '[BO] Tests',
      },
      {
        key: 'user',
        label: 'User',
      },
      {
        key: 'test_create_bo_1124',
        label: 'test create bo 1124',
      },
      {
        key: 'qcys2602',
        label: '[QC]YS2602',
      },
      {
        key: 'update_description',
        label: 'update description edit',
      },
      {
        key: 'n_test_2381w8',
        label: 'n test 2381w8',
      },
      {
        key: 'test_ba',
        label: 'Test BA',
      },
      {
        key: 'test_ovent',
        label: 'Test Ovent',
      },
      {
        key: 'sql_check',
        label: 'SQL CHECK',
      },
      {
        key: 'create_bo_222',
        label: 'create BO 22/2 updated',
      },
      {
        key: 'fe_test_rule_create',
        label: '[FE] Test Rule Create',
      },
      {
        key: 'qct_t_check',
        label: '[QCT] T check',
      },
      {
        key: 'ys2000',
        label: 'YS2000',
      },
      {
        key: 'test_create_bo_12344',
        label: 'test create bo 12344',
      },
      {
        key: 'test_create_bo',
        label: 'test create BO',
      },
      {
        key: 'tets_to_mi',
        label: 'tets tạo mới',
      },
      {
        key: 'test_permission_1',
        label: 'test permission 1',
      },
      {
        key: 'n_test_5',
        label: 'n test 5',
      },
      {
        key: 'bo_mi_n',
        label: 'BO mới nè',
      },
      {
        key: 'anh_yu_em_rt_nhiu',
        label: 'Anh yêu em rất nhiều',
      },
      {
        key: 'test_permission_4',
        label: 'test permission 4',
      },
      {
        key: 'testttttttttttttt',
        label: 'testttttttttttttt',
      },
      {
        key: 'test_permission_2',
        label: 'test permission 2',
      },
      {
        key: 'bo_lin_test',
        label: 'BO Lin Test',
      },
      {
        key: 'app_inbox',
        label: 'Smart Inbox',
      },
      {
        key: 'save_owner_id_2',
        label: 'save owner id 2',
      },
      {
        key: 'cuong_test_',
        label: 'cuong test ',
      },
      {
        key: 'bo_lin_test_1',
        label: 'BO Lin Test ',
      },
      {
        key: 'test_create_on_behalf_2',
        label: 'test create on behalf 2',
      },
      {
        key: 'test_owner_1123',
        label: 'test owner 1123',
      },
      {
        key: 'add_account',
        label: 'add account',
      },
      {
        key: 'nhi1238u',
        label: 'nhi1238u test 1',
      },
      {
        key: 'test_permission_5',
        label: 'test permission 5',
      },
      {
        key: 'test_permission',
        label: 'test permission',
      },
      {
        key: 'huytc_test_new',
        label: '[huytc] test new',
      },
      {
        key: 'test_full_to_chn_acc_khc',
        label: '[Test full] tạo chọn acc khác',
      },
      {
        key: 'huytx_test_create_1',
        label: '[huytx] test create 1',
      },
      {
        key: 'n_test_6',
        label: 'n test 6',
      },
      {
        key: 'testttttttttttttt2222222',
        label: 'testttttttttttttt2222222',
      },
      {
        key: 'huytx_test_sync_bo_from_admin',
        label: '[huytx] test sync BO from admin updated',
      },
      {
        key: 'test_owner_1124',
        label: 'test owner 1124',
      },
      {
        key: 'sample_transaction',
        label: 'Sample transaction',
      },
      {
        key: 'test_create_bo_2_owned_by_user',
        label: 'test create BO 2 (owned by user)',
      },
      {
        key: 'thanh_change_group_01',
        label: 'Thanh change group 01 +1',
      },
      {
        key: 'transaction_rfm',
        label: 'Transaction RFM',
      },
      {
        key: 'test2000',
        label: 'test2000',
      },
      {
        key: 'test_create_bo_for_own',
        label: 'test create bo for own',
      },
      {
        key: 'test_create_bo_for_another_user',
        label: 'test create bo for another user',
      },
      {
        key: 'transfer_ownership',
        label: 'transfer ownership',
      },
      {
        key: 'test_create_bo_for_another_user_02',
        label: 'test create bo for another user 02',
      },
      {
        key: 'n_test_8',
        label: 'n test 8',
      },
      {
        key: 'feeeeeeee',
        label: 'Feeeeeeee',
      },
      {
        key: 'huyyyyyyyyyy',
        label: "XAAA'<img src=1 onerror=alert(origin)>",
      },
      {
        key: 'videodemoxss',
        label: 'Videodemo-XSS',
      },
      {
        key: 'demo_xss',
        label: 'Dem444444',
      },
      {
        key: 'demo_xssa',
        label: 'Demo XSS',
      },
      {
        key: 'check_li_object_owner_id',
        label: 'check lại object_owner_id',
      },
      {
        key: 'xss',
        label: 'XSS',
      },
      {
        key: 'test_create_bo_02',
        label: 'test create BO 02',
      },
      {
        key: 'video_demo',
        label: 'Video demo',
      },
      {
        key: 'n_test_3',
        label: 'n test 3',
      },
      {
        key: 'n_test_4',
        label: 'n test 4',
      },
      {
        key: 'bo_lifecycle',
        label: 'BO Lifecycle',
      },
      {
        key: 'qc_test',
        label: 'QC test',
      },
      {
        key: 'test_create_bo__01_owned_by_user',
        label: 'test create BO  01 (owned by user)',
      },
      {
        key: 'test_feeeeeeeeeee',
        label: 'test feeeeeeeeeee',
      },
      {
        key: 'qc_thanh_123',
        label: 'qc thanh 123',
      },
      {
        key: 'label',
        label: 'label',
      },
      {
        key: 'test_111111111111111111111111111111111111',
        label: 'Test 111111111111111111111111111111111111',
      },
      {
        key: 'to_t_account_khc',
        label: 'tạo từ account khác',
      },
      {
        key: 'huytc_test_cache',
        label: 'huytc test cache',
      },
      {
        key: 'bo_test_import_from_cdp',
        label: 'BO test import from CDP',
      },
      {
        key: 'created_by_user',
        label: 'created by user edit created by user',
      },
      {
        key: 'inbox',
        label: 'Inbox',
      },
      {
        key: 'user_birthday',
        label: 'User birthday',
      },
      {
        key: 'huytc_test_68',
        label: '[huytc] test 68',
      },
      {
        key: 'fe_test_bo',
        label: '[FE] Test BO',
      },
      {
        key: 'n_test_1',
        label: 'n test 1',
      },
      {
        key: 'promotion_pool',
        label: 'Promotion Pool',
      },
      {
        key: 'to_created_by_user',
        label: 'tạo created by user',
      },
      {
        key: 'n_test_9',
        label: 'n test 9',
      },
      {
        key: 'nhi_test_10',
        label: 'nhi test 10',
      },
      {
        key: 'isp',
        label: 'ISP',
      },
      {
        key: 'test_li_bug_k_hin_bo',
        label: 'test lại bug k hiện BO',
      },
      {
        key: 'mitu_bo_003',
        label: 'MiTu BO 003',
      },
      {
        key: 'fe_test_create_attribute',
        label: '[FE] Test Create Attribute',
      },
      {
        key: 'qc123',
        label: '[QC]123',
      },
      {
        key: 'thangbt_test_object_412_3',
        label: 'thangbt test object 4/12 #3',
      },
      {
        key: 'test',
        label: 'Attribute',
      },
      {
        key: 'n_check_3',
        label: 'n check 3',
      },
      {
        key: 'test_li_bug_k_hin_bo_2',
        label: 'test lại bug k hiện BO 2',
      },
      {
        key: 'anh_yu_em_rt_nhiu_1123213',
        label: 'Anh yêu em rất nhiều 1123213',
      },
      {
        key: 'n_check_2',
        label: 'n check 2',
      },
      {
        key: 'test_create_on_behalf_another_user_01',
        label: 'test create on behalf another user 01',
      },
      {
        key: 'customers',
        label: 'Customer',
      },
      {
        key: 'test_fe_1111111111111111',
        label: 'Test Fe 1111111111111111',
      },
      {
        key: 'test_bo',
        label: 'test bo',
      },
      {
        key: 'qc_test_ys2000',
        label: '[QC] Test YS2000',
      },
      {
        key: 'destination',
        label: 'Destination',
      },
      {
        key: 'fe_first_test',
        label: 'FE First Test',
      },
      {
        key: 'bk7_test_create_bo',
        label: 'BK7 test create BO',
      },
      {
        key: 'article',
        label: 'Article',
      },
      {
        key: 'h_test_12',
        label: '[H] Test 12',
      },
      {
        key: 'n_to_mi',
        label: 'N tạo mới',
      },
      {
        key: 'n_to_mi_2',
        label: 'N tạo mới 2',
      },
      {
        key: 'save_owner_id',
        label: 'save owner id',
      },
      {
        key: 'dat_create_bo',
        label: '[Dat] Create BO',
      },
      {
        key: 'c_test123123',
        label: '[C] Test123123',
      },
      {
        key: 'feeeeeeee1111111111111111',
        label: 'Feeeeeeee1111111111111111',
      },
      {
        key: 'nhitest_test_9696',
        label: '[NhiTest] test 9696',
      },
      {
        key: 'fe_test_123123',
        label: 'Fe test 123123',
      },
      {
        key: 'sgmt_object_test',
        label: 'QC test portal',
      },
      {
        key: 'fe_1024_1511',
        label: 'Fe 10:24 15/11',
      },
      {
        key: 'test_fefefefefe',
        label: 'test fefefefefe',
      },
      {
        key: 'abc_1511',
        label: 'ABC 15/11',
      },
      {
        key: 'fe_test_123123123123123',
        label: 'Fe test 123123123123123',
      },
      {
        key: 'to',
        label: 'tạo',
      },
      {
        key: 'test_create_object_on_behalf_another',
        label: 'test create object on behalf another',
      },
      {
        key: 'tessssssssssssssss',
        label: 'tessssssssssssssss',
      },
      {
        key: 'country',
        label: 'Country',
      },
      {
        key: 'customer_users',
        label: 'Customer User',
      },
      {
        key: 'huytc_test_70',
        label: '[huytc] test 70',
      },
      {
        key: 'than_test_attribute_is_delete',
        label: 'Than test attribute is_delete',
      },
      {
        key: 'to_2',
        label: 'tạo 2',
      },
      {
        key: 'qc_trinh_test',
        label: 'QC Trinh test',
      },
      {
        key: 'thanh_qc_test_bo',
        label: 'Thanh QC test BO 1234',
      },
      {
        key: 'huytc_test_69',
        label: '[huytc] test 69',
      },
      {
        key: 'ad_zone',
        label: 'Ad Zone',
      },
      {
        key: 'qc_test_01',
        label: 'QC Test 011',
      },
      {
        key: 'campaign',
        label: 'Campaign',
      },
      {
        key: 'nhi_test_1',
        label: 'nhi test 1',
      },
      {
        key: 'core_test_create_bo_v1',
        label: 'core_test_create_bo_v1',
      },
      {
        key: 'lin_create_new_bo',
        label: 'Lin create new BO',
      },
      {
        key: 'fe_test_items_1',
        label: '[FE] Test Items ',
      },
      {
        key: 'unlabeld_data_object20240519s_135349',
        label: 'Unlabeld data object#2024-05-19s 13:53:49',
      },
      {
        key: 'test_fe_1511',
        label: 'Test Fe 15/11',
      },
      {
        key: 'unlabeld_data_object20240520_182231',
        label: 'Unlabeld data object#2024-05-20 18:22:31',
      },
      {
        key: 'test_cuong_12312312312',
        label: 'test Cuong 12312312312',
      },
      {
        key: 'c_test1231232222',
        label: '[C] Test123123222',
      },
      {
        key: 'c_test12312322',
        label: '[C] Test1231232',
      },
      {
        key: 'bo_test_rfm',
        label: '[BO] Test RFM editsss',
      },
      {
        key: 'test__11',
        label: 'TEST__11',
      },
      {
        key: 'product',
        label: 'Product',
      },
      {
        key: 'unlabeld_data_object20240528_1544ueazxcv',
        label: 'Unlabeld data object#2024-05-28 15:44:æœUeazxcv',
      },
      {
        key: 'n_tets_bo',
        label:
          'nmmxnbbcvnzxvcbdvcbnsvdnvcshdvcsvcbnsvcnbsvcsbnvcnbsvcndscsvnbdsvcnsandsncoqijsakqnajsbwvdwvdgwdwvcdgwgdcwccvcvwcdwwdewdw',
      },
      {
        key: 't_test_bo_01',
        label: 'T test bo 01',
      },
      {
        key: 'huy_create_bo',
        label: 'huy create BO',
      },
      {
        key: 'sakura',
        label: 'SAKURA',
      },
      {
        key: 'subscribe',
        label: 'Subscriber',
      },
      {
        key: 'test_ab',
        label: 'Test AB',
      },
      {
        key: 'hoang_create_bo',
        label: '[Hoang] Create BO',
      },
      {
        key: 'test_create_on_behalf_another_user',
        label: 'test create on behalf another user',
      },
      {
        key: 'fasaf',
        label: 'Test conversion',
      },
      {
        key: 'huy_create_create',
        label: 'huy create create',
      },
      {
        key: 'neu',
        label: 'neu',
      },
      {
        key: 'test_10',
        label: 'test 10',
      },
      {
        key: 'transaction',
        label: 'transaction',
      },
    ],
  },
  {
    key: 'dataSource',
    label: 'Data Source',
    children: [
      {
        key: '556434020',
        label: 'Aristino Source',
        children: [
          {
            key: '-104:-11',
            label: 'Remove from cart',
          },
          {
            key: '-105:-11',
            label: 'Checkout cart',
          },
          {
            key: '3391:3392',
            label: 'Promotion code used',
          },
          {
            key: '-102:-11',
            label: 'View product',
          },
          {
            key: '-103:-11',
            label: 'Add to cart',
          },
          {
            key: '-102:-20',
            label: 'View page',
          },
          {
            key: '12:-11',
            label: 'View cart',
          },
          {
            key: '4:5',
            label: 'Identify user',
          },
          {
            key: '-106:-11',
            label: 'Purchase',
          },
          {
            key: '41:5',
            label: 'User Sign in',
          },
          {
            key: '50:5',
            label: 'User Sign up',
          },
          {
            key: '498762:5',
            label: 'User Exit Intent',
          },
        ],
      },
      {
        key: '556430427',
        label: 'Kiểm tra hôm nay ngày 22 tháng 1',
        children: [
          {
            key: '-103:-11',
            label: 'Add to cart',
          },
          {
            key: '489283:2283945',
            label: 'Kiểm tra ngày 22 tháng 1 lần 1',
          },
          {
            key: '1631:9',
            label: 'test fefefefef123123',
          },
          {
            key: '-102:-20',
            label: 'View page',
          },
          {
            key: '792672:-20',
            label: 'click view page',
          },
          {
            key: '-101:-11',
            label: 'Click product',
          },
          {
            key: '896830:779154',
            label: 'click',
          },
          {
            key: '1402916:1402918',
            label: 'User sign out',
          },
          {
            key: '41:5',
            label: 'User Sign in',
          },
          {
            key: '471165:5546676',
            label: 'Verify OTP Successfully',
          },
        ],
      },
      {
        key: '556428109',
        label: 'Source test fefefefe',
        children: [
          {
            key: '1631:9',
            label: 'test fefefefef123123',
          },
        ],
      },
      {
        key: '556428108',
        label: 'Source Fe 123',
        children: [
          {
            key: '1631:9',
            label: 'test fefefefef123123',
          },
        ],
      },
      {
        key: '556428101',
        label: 'Kiểm tra lại event ngày 16 tháng 1',
        children: [
          {
            key: '1631:9',
            label: 'test fefefefef123123',
          },
          {
            key: '-103:-11',
            label: 'Add to cart',
          },
        ],
      },
      {
        key: '556427944',
        label: 'kiểm tra lại event lần 11',
        children: [
          {
            key: '50:5',
            label: 'User Sign up',
          },
          {
            key: '-106:-11',
            label: 'Purchase',
          },
          {
            key: '1631:9',
            label: 'test fefefefef123123',
          },
          {
            key: '41:5',
            label: 'User Sign in',
          },
          {
            key: '-103:-11',
            label: 'Add to cart',
          },
          {
            key: '-102:-20',
            label: 'View page',
          },
          {
            key: '-102:-11',
            label: 'View product',
          },
        ],
      },
      {
        key: '556427943',
        label: 'test lại event lần 1',
        children: [
          {
            key: '-106:-11',
            label: 'Purchase',
          },
          {
            key: '-103:-11',
            label: 'Add to cart',
          },
          {
            key: '-101:-23',
            label: 'Ads clicked',
          },
        ],
      },
      {
        key: '556380312',
        label: 'News Source',
        children: [
          {
            key: '41:5',
            label: 'User Sign in',
          },
          {
            key: '50:5',
            label: 'User Sign up',
          },
          {
            key: '-128:-23',
            label: 'Ads might be viewed',
          },
          {
            key: '4:5',
            label: 'Identify user',
          },
          {
            key: '-102:-20',
            label: 'View page',
          },
        ],
      },
      {
        key: '556378356',
        label: 'demoaa',
        children: [
          {
            key: '1595776:-23',
            label: 'Test Media',
          },
        ],
      },
      {
        key: '556376478',
        label: 'Predictive Events',
        children: [
          {
            key: '5084109:5084110',
            label: 'Audience movement',
          },
          {
            key: '5084109:5861663',
            label: 'Personas Movement',
          },
        ],
      },
      {
        key: '556369994',
        label: 'Kiểm tra luồng tạo cơ bản lần 4',
        children: [
          {
            key: '-103:-20',
            label: 'Test kiểm tra GD mới của Create new event',
          },
          {
            key: '-106:-20',
            label: 'Kiểm tra lại luồng cơ bản lần 2',
          },
        ],
      },
      {
        key: '556369993',
        label: 'Kiểm tra luồng tạo cơ bản lần 3',
        children: [
          {
            key: '-106:-20',
            label: 'Kiểm tra lại luồng cơ bản lần 2',
          },
          {
            key: '487224:11',
            label: 'Kiểm tra fix bug luồng tạo cơ bản',
          },
        ],
      },
      {
        key: '556357615',
        label: 'Test SANDBOX',
        children: [
          {
            key: '-106:-11',
            label: 'Purchase',
          },
          {
            key: '12:-11',
            label: 'View cart',
          },
          {
            key: '-103:-11',
            label: 'Add to cart',
          },
        ],
      },
      {
        key: '556357181',
        label: 'Test icon',
        children: [
          {
            key: '-105:-11',
            label: 'Checkout cart',
          },
          {
            key: '-103:-11',
            label: 'Add to cart',
          },
        ],
      },
      {
        key: '556356960',
        label: 'Server Ha',
        children: [
          {
            key: '-128:17',
            label: 'Screen View',
          },
          {
            key: '1:489290',
            label: 'HAPPY',
          },
        ],
      },
      {
        key: '556356959',
        label: 'mobile ha',
        children: [
          {
            key: '1743669:1743670',
            label: 'Create 2',
          },
          {
            key: '-102:-20',
            label: 'View page',
          },
        ],
      },
      {
        key: '556356635',
        label: 'T test Line/Zalo',
        children: [
          {
            key: '-102:-20',
            label: 'View page',
          },
        ],
      },
      {
        key: '556356103',
        label: '[KhanhHV] Mobile App SB',
        children: [
          {
            key: '45:15',
            label: 'Filter product list (old)',
          },
          {
            key: '-102:-11',
            label: 'View product',
          },
          {
            key: '-104:-11',
            label: 'Remove from cart',
          },
          {
            key: '1627:-23',
            label: 'Ads requested to sent',
          },
          {
            key: '-106:-11',
            label: 'Purchase',
          },
          {
            key: '1631:-23',
            label: 'Invalid request tracked',
          },
          {
            key: '29:3',
            label: 'Reset anonymous (old)',
          },
          {
            key: '13:3',
            label: 'User sign out (old)',
          },
          {
            key: '41:5',
            label: 'User Sign in',
          },
          {
            key: '45:8',
            label: 'Filter product list',
          },
          {
            key: '-103:-11',
            label: 'Add to cart',
          },
          {
            key: '44:15',
            label: 'Search product (old)',
          },
          {
            key: '1629:-23',
            label: 'Soft bounce tracked',
          },
          {
            key: '-107:-11',
            label: 'Enter payment info',
          },
          {
            key: '1628:-23',
            label: 'Hard bounce tracked',
          },
          {
            key: '11:15',
            label: 'View product list (old)',
          },
          {
            key: '29:5',
            label: 'Reset anonymous',
          },
          {
            key: '-102:16',
            label: 'View screen (old)',
          },
          {
            key: '-102:-23',
            label: 'View Advertising',
          },
          {
            key: '37:8',
            label: 'View product list (old)',
          },
          {
            key: '1632:-23',
            label: 'Timeout request tracked',
          },
          {
            key: '37:15',
            label: 'View product list (old)',
          },
          {
            key: '-102:7',
            label: 'View screen',
          },
          {
            key: '16:3',
            label: 'Reset anonymous (old)',
          },
          {
            key: '-102:-20',
            label: 'View page',
          },
          {
            key: '-127:-23',
            label: 'Ads delivered (impression)',
          },
          {
            key: '14:8',
            label: 'Filter product list (old)',
          },
          {
            key: '4:5',
            label: 'Identify user',
          },
          {
            key: '10:15',
            label: 'Search product (old)',
          },
          {
            key: '12:-11',
            label: 'View cart',
          },
          {
            key: '6:5',
            label: 'Identify user (old)',
          },
          {
            key: '42:-11',
            label: 'View cart (old)',
          },
          {
            key: '-128:-23',
            label: 'Ads might be viewed',
          },
          {
            key: '43:5',
            label: 'User sign out (old)',
          },
          {
            key: '11:8',
            label: 'View product list',
          },
          {
            key: '1633:-23',
            label: 'Destination frequency capping',
          },
          {
            key: '41:3',
            label: 'User Sign in (old)',
          },
          {
            key: '50:5',
            label: 'User Sign up',
          },
          {
            key: '14:15',
            label: 'Filter product list (old)',
          },
          {
            key: '1627:3392',
            label: 'Promotion code sent',
          },
          {
            key: '4045:4046',
            label: 'Request update segment',
          },
          {
            key: '4:3',
            label: 'Identify user (old)',
          },
          {
            key: '3391:3392',
            label: 'Promotion code used',
          },
          {
            key: '6:3',
            label: 'Identify user (old)',
          },
          {
            key: '1635:1637',
            label: 'Track journey operation',
          },
          {
            key: '44:8',
            label: 'Search product (old)',
          },
          {
            key: '13:5',
            label: 'User sign out',
          },
          {
            key: '10:8',
            label: 'Search product',
          },
          {
            key: '-105:-11',
            label: 'Checkout cart',
          },
          {
            key: '1636:1637',
            label: 'BO info updated by journey',
          },
          {
            key: '-101:-23',
            label: 'Ads clicked',
          },
          {
            key: '-101:-11',
            label: 'Click product',
          },
          {
            key: '-128:17',
            label: 'Screen View',
          },
          {
            key: '43:3',
            label: 'User sign out (old)',
          },
          {
            key: '50:3',
            label: 'User Sign up (old)',
          },
          {
            key: '16:5',
            label: 'Reset anonymous (old)',
          },
          {
            key: '1727:-23',
            label: 'Delivery system error',
          },
        ],
      },
      {
        key: '556351881',
        label: 'Conversions',
        children: [
          {
            key: '5295628:1280229',
            label: 'Conversion [thanh] test event delivery',
          },
          {
            key: '5085289:1280229',
            label: 'Conversion aaaaaaaaaa',
          },
          {
            key: '1386897:1280229',
            label: 'Conversion thanh test visitor conversion',
          },
          {
            key: '1385382:1280229',
            label: 'Conversion thanh test customer conversion',
          },
          {
            key: '5624026:1280229',
            label: 'Conversion Blast Campaign',
          },
          {
            key: '5625283:1280229',
            label: 'Conversion Blast Campaign 2',
          },
          {
            key: '5334364:1280229',
            label: 'Conversion test event delivery 2',
          },
          {
            key: '5691865:1280229',
            label: 'Conversion [T] TEST Create conversion',
          },
          {
            key: '5697158:1280229',
            label: 'Conversion Test 123123',
          },
          {
            key: '5701009:1280229',
            label: 'Conversion [Tinh] Test settings',
          },
          {
            key: '5702937:1280229',
            label: 'Conversion [T] test default',
          },
          {
            key: '5705053:1280229',
            label: 'Conversion customer conversion 01',
          },
          {
            key: '5707800:1280229',
            label: 'Conversion [K] Journey Goal Object',
          },
          {
            key: '5771618:1280229',
            label: 'Conversion [Tinh] TEST CONVERSION',
          },
          {
            key: '5691872:1280229',
            label: 'Conversion fasdfasdfasdf',
          },
          {
            key: '5703008:1280229',
            label: 'Conversion [T] test create',
          },
          {
            key: '5705758:1280229',
            label: 'Conversion Test Thienpdt fe',
          },
          {
            key: '5774980:1280229',
            label: 'Conversion [t] create conersion',
          },
          {
            key: '5691960:1280229',
            label: 'Conversion [Tinh] test create conversion',
          },
          {
            key: '5703023:1280229',
            label: 'Conversion [T] Test create new',
          },
          {
            key: '5772663:1280229',
            label: 'Conversion [T] Conversion code exits',
          },
          {
            key: '5777264:1280229',
            label: 'Conversion [T] test 111111',
          },
          {
            key: '5779527:1280229',
            label: 'Conversion [T] TEST CONVERSION EXIST LAN @',
          },
          {
            key: '5790063:1280229',
            label: 'Conversion  [T] new coversion aaaa',
          },
          {
            key: '5820379:1280229',
            label: 'Conversion test journey',
          },
          {
            key: '5837059:1280229',
            label: 'Conversion [K] Conversion with BO Id - Event attr',
          },
          {
            key: '5847578:1280229',
            label: 'Conversion test journey template',
          },
          {
            key: '5870741:1280229',
            label: 'Conversion nhi test journey template',
          },
          {
            key: '5692306:1280229',
            label: 'Conversion [T] test tets',
          },
          {
            key: '5772666:1280229',
            label: 'Conversion Conversion exist ghfghdfghfgh',
          },
          {
            key: '1377664:1280229',
            label: 'Conversion multi event 3',
          },
          {
            key: '1366535:1280229',
            label: 'Conversion N test Purchase filter theo channel',
          },
          {
            key: '1366871:1280229',
            label: 'Conversion Multi event',
          },
          {
            key: '1792016:1280229',
            label: 'Conversion [thanh] visitor conversion (transaction - viewable)',
          },
          {
            key: '1388348:1280229',
            label: 'Conversion test view product',
          },
          {
            key: '1388345:1280229',
            label: 'Conversion test view page',
          },
          {
            key: '1366873:1280229',
            label: 'Conversion Multi event 2',
          },
        ],
      },
      {
        key: '556336465',
        label: 'Website vercel',
        children: [
          {
            key: '-107:-11',
            label: 'Enter payment info',
          },
          {
            key: '12:-11',
            label: 'View cart',
          },
          {
            key: '40:1082739',
            label: 'antsomi webpush subscribe',
          },
          {
            key: '471165:-15',
            label: 'Submit transaction',
          },
          {
            key: '1394296:1394297',
            label: 'thanh test new event',
          },
          {
            key: '-106:-11',
            label: 'Purchase',
          },
          {
            key: '-105:-11',
            label: 'Checkout cart',
          },
          {
            key: '1629:-23',
            label: 'Soft bounce tracked',
          },
          {
            key: '-102:-11',
            label: 'View product',
          },
          {
            key: '-103:-11',
            label: 'Add to cart',
          },
          {
            key: '-102:7',
            label: 'View screen',
          },
          {
            key: '-104:-11',
            label: 'Remove from cart',
          },
          {
            key: '-102:-20',
            label: 'View page',
          },
          {
            key: '1635:489245',
            label: 'test new event',
          },
          {
            key: '48:-11',
            label: 'product add_wish_list',
          },
          {
            key: '10:8',
            label: 'Search product',
          },
          {
            key: '471165:481690',
            label: 'Submit lead',
          },
        ],
      },
      {
        key: '556301985',
        label: '[QC] T source Blog',
        children: [
          {
            key: '-102:17',
            label: 'screen view',
          },
          {
            key: '-102:-20',
            label: 'View page',
          },
        ],
      },
      {
        key: '556301560',
        label: 'CÁTCÁT',
        children: [
          {
            key: '-102:-20',
            label: 'View page',
          },
          {
            key: '471165:481690',
            label: 'Submit lead',
          },
        ],
      },
      {
        key: '556301517',
        label: 'Ha',
        children: [
          {
            key: '24:-20',
            label: 'Create 3',
          },
          {
            key: '1312300:171605',
            label: 'Create 6',
          },
          {
            key: '-102:-20',
            label: 'View page',
          },
          {
            key: '10:8',
            label: 'Search product',
          },
          {
            key: '-101:-11',
            label: 'Click product',
          },
          {
            key: '-115:-23',
            label: 'trung thu',
          },
        ],
      },
      {
        key: '556301499',
        label: 'KhanhHV Mobile Testing Final',
        children: [
          {
            key: '530989:7',
            label: 'Mobile Webview',
          },
        ],
      },
      {
        key: '556301497',
        label: 'Mobile App',
        children: [
          {
            key: '-102:7',
            label: 'View screen',
          },
          {
            key: '-102:-11',
            label: 'View product',
          },
          {
            key: '-101:-11',
            label: 'Click product',
          },
          {
            key: '11:8',
            label: 'View product list',
          },
          {
            key: '40:1082739',
            label: 'antsomi webpush subscribe',
          },
          {
            key: '1302983:1302984',
            label: 'App Push Subscribe',
          },
          {
            key: '12:-11',
            label: 'View cart',
          },
          {
            key: '41:5',
            label: 'User Sign in',
          },
          {
            key: '-103:-11',
            label: 'Add to cart',
          },
          {
            key: '50:5',
            label: 'User Sign up',
          },
          {
            key: '4:5',
            label: 'Identify user',
          },
          {
            key: '10:8',
            label: 'Search product',
          },
          {
            key: '530989:7',
            label: 'Mobile Webview',
          },
          {
            key: '-102:-20',
            label: 'View page',
          },
        ],
      },
      {
        key: '556301460',
        label: 'all in campu',
        children: [
          {
            key: '-102:-20',
            label: 'View page',
          },
        ],
      },
      {
        key: '556301429',
        label: 'POS',
        children: [
          {
            key: '1284450:171605',
            label: 'Create 5',
          },
          {
            key: '24:-20',
            label: 'Create 3',
          },
          {
            key: '-106:-11',
            label: 'Purchase',
          },
          {
            key: '-102:-11',
            label: 'View product',
          },
          {
            key: '757967:5',
            label: 'Send message to page',
          },
          {
            key: '-101:-23',
            label: 'Ads clicked',
          },
          {
            key: '1148782:1148785',
            label: 'Event check new import',
          },
          {
            key: '-106:-15',
            label: 'Transaction Event',
          },
        ],
      },
      {
        key: '556301423',
        label: 'Lead',
        children: [
          {
            key: '-102:-20',
            label: 'View page',
          },
          {
            key: '471165:5546676',
            label: 'Verify OTP Successfully',
          },
          {
            key: '471165:5',
            label: 'Submit rating',
          },
          {
            key: '-106:-15',
            label: 'Transaction Event',
          },
          {
            key: '471165:481690',
            label: 'Submit lead',
          },
        ],
      },
      {
        key: '556301357',
        label: 'Source website D xem (new)',
        children: [
          {
            key: '1743669:1743670',
            label: 'Create 2',
          },
          {
            key: '1742455:1740260',
            label: 'lin test icon event 1',
          },
          {
            key: '1312300:171605',
            label: 'Create 6',
          },
          {
            key: '-102:-21',
            label: 'Test lại event cũ',
          },
          {
            key: '-105:-11',
            label: 'Checkout cart',
          },
          {
            key: '48:7',
            label: 'Khang create event 11 10 17 36',
          },
          {
            key: '7:-15',
            label: 'Thành test',
          },
        ],
      },
      {
        key: '556300938',
        label: 'Loyalty Programs',
        children: [
          {
            key: '1627:3392',
            label: 'Promotion code sent',
          },
          {
            key: '3391:3392',
            label: 'Promotion code used',
          },
        ],
      },
      {
        key: '556300709',
        label: 'Delivery',
        children: [
          {
            key: '1631:-23',
            label: 'Invalid request tracked',
          },
          {
            key: '-103:-11',
            label: 'Add to cart',
          },
          {
            key: '895142:-23',
            label: 'story_frequency_capping',
          },
          {
            key: '1595776:-23',
            label: 'Test Media',
          },
          {
            key: '24:-20',
            label: 'Create 3',
          },
          {
            key: '1312300:171605',
            label: 'Create 6',
          },
          {
            key: '-101:16',
            label: 'Event H',
          },
          {
            key: '1628:-23',
            label: 'Hard bounce tracked',
          },
          {
            key: '1243493:-23',
            label: 'Entered Control Group',
          },
          {
            key: '4045:4046',
            label: 'Request update segment',
          },
          {
            key: '1360674:-23',
            label: '3rd_party_seen',
          },
          {
            key: '1627:-23',
            label: 'Ads requested to sent',
          },
          {
            key: '-128:-23',
            label: 'Viewable advertising',
          },
          {
            key: '-107:-20',
            label: 'themmm',
          },
          {
            key: '1636:1637',
            label: 'BO info updated by journey',
          },
          {
            key: '895143:-23',
            label: 'missing_receiver_tracking',
          },
          {
            key: '1629:-23',
            label: 'Soft bounce tracked',
          },
          {
            key: '970186:-23',
            label: '3rd_campaign_tracking',
          },
          {
            key: '14:-23',
            label: 'Error tracking',
          },
          {
            key: '-127:-23',
            label: 'Ads delivered (impression)',
          },
          {
            key: '-102:-23',
            label: 'View Advertising',
          },
          {
            key: '-101:-23',
            label: 'Ads clicked',
          },
          {
            key: '1635:1637',
            label: 'Track journey operation',
          },
          {
            key: '1633:-23',
            label: 'Destination frequency capping',
          },
          {
            key: '1632:-23',
            label: 'Timeout request tracked',
          },
        ],
      },
      {
        key: '556300706',
        label: 'Website',
        children: [
          {
            key: '-102:-20',
            label: 'View page',
          },
          {
            key: '44:15',
            label: 'Search product',
          },
          {
            key: '1402916:1402918',
            label: 'User sign out',
          },
          {
            key: '1312300:171605',
            label: 'Create 6',
          },
          {
            key: '4:5',
            label: 'Identify user',
          },
          {
            key: '13:5',
            label: 'User sign out',
          },
          {
            key: '779153:779154',
            label: 'Close popup',
          },
          {
            key: '-105:-11',
            label: 'Checkout cart',
          },
          {
            key: '471165:634206',
            label: 'Submit subscription',
          },
          {
            key: '-101:-11',
            label: 'Click product',
          },
          {
            key: '50:1402918',
            label: 'User_Sign_Up',
          },
          {
            key: '41:5',
            label: 'User Sign in',
          },
          {
            key: '50:5',
            label: 'User Sign up',
          },
          {
            key: '-102:557517',
            label: 'View Collection',
          },
          {
            key: '471165:481690',
            label: 'Submit lead',
          },
          {
            key: '1302983:1302984',
            label: 'App Push Subscribe',
          },
          {
            key: '10:8',
            label: 'Search product',
          },
          {
            key: '-103:-11',
            label: 'Add to cart',
          },
          {
            key: '471165:-15',
            label: 'Submit transaction',
          },
          {
            key: '37:-11',
            label: 'View Product list (custom)',
          },
          {
            key: '12:-11',
            label: 'View cart',
          },
          {
            key: '37:15',
            label: 'View product list',
          },
          {
            key: '498762:5',
            label: 'User Exit Intent',
          },
          {
            key: '25:171605',
            label: 'Create 4',
          },
          {
            key: '-104:-11',
            label: 'Remove from cart',
          },
          {
            key: '-102:-11',
            label: 'View product',
          },
          {
            key: '48:-11',
            label: 'product add_wish_list',
          },
          {
            key: '-106:-11',
            label: 'Purchase',
          },
          {
            key: '-101:16',
            label: 'Event H',
          },
          {
            key: '6:3',
            label: 'User identify',
          },
          {
            key: '-102:-23',
            label: 'View Advertising',
          },
          {
            key: '42:-11',
            label: 'View cart (old)',
          },
          {
            key: '1743669:1743670',
            label: 'Create 2',
          },
          {
            key: '40:1082739',
            label: 'antsomi webpush subscribe',
          },
        ],
      },
    ],
  },
  {
    key: 'at-table',
    label: 'Analytics Tables',
    children: [
      {
        key: 'table_test_time_range',
        label: 'Table test time range',
      },
      {
        key: 'thanh_test_export_wp',
        label: 'thanh_test_export_wp',
      },
      {
        key: 'product_with_price__0',
        label: 'Product with price > 0',
      },
      {
        key: 'thanh_test_export',
        label: 'thanh_test_export',
      },
      {
        key: 'table_check_tracked_time_new_02',
        label: 'Table check tracked time new 02',
      },
      {
        key: 'mentest01',
        label: 'mentest01',
      },
      {
        key: 'mennn',
        label: 'mennn',
      },
      {
        key: 'mentest',
        label: 'mentest',
      },
      {
        key: 'men',
        label: 'men',
      },
      {
        key: 'core_test_table',
        label: 'Core test table',
      },
      {
        key: 'table_6',
        label: 'table 6',
      },
      {
        key: 'table_123111',
        label: 'table 123111',
      },
      {
        key: 'table_4',
        label: 'table 4',
      },
      {
        key: 'table_3',
        label: 'table 3',
      },
      {
        key: 'table2',
        label: 'table2',
      },
      {
        key: 'table',
        label: 'table',
      },
      {
        key: 'dat_test_table',
        label: 'Dat test table',
      },
      {
        key: 'table_1',
        label: 'table 1',
      },
      {
        key: 'table_at_product_new_lan_2',
        label: 'Table AT PRODUCT NEW LAN 2',
      },
      {
        key: 'table_at_product_new',
        label: 'Table AT PRODUCT NEW',
      },
      {
        key: 'bushtin_3333',
        label: 'BushTin_3333',
      },
      {
        key: 'table_trung_name_column',
        label: 'table trung name column',
      },
      {
        key: 'table_new_83',
        label: 'table new 8/3',
      },
      {
        key: 'check_run_cmt_40',
        label: 'check run cmt 4.0',
      },
      {
        key: 'table_check_cmt_20',
        label: 'Table check cmt 2.0',
      },
      {
        key: 'table_check_cmt',
        label: 'Table check cmt',
      },
      {
        key: 'table_check_ma_hoa',
        label: 'Table check ma hoa',
      },
      {
        key: 'table_check_error_2',
        label: 'Table check error 2',
      },
      {
        key: 'table_check_partition_new',
        label: 'Table check Partition new',
      },
      {
        key: 'table_check_partition',
        label: 'Table check Partition',
      },
      {
        key: 'table_check_nhieu_row',
        label: 'table check nhiều row',
      },
      {
        key: 'table_vinlt_4',
        label: 'table_vinlt_4',
      },
      {
        key: 'table_vinlt_3',
        label: 'table_vinlt_3',
      },
      {
        key: 'vinlt_table_1',
        label: 'vinlt_table_1',
      },
      {
        key: 'table_1_7',
        label: 'Table 1.7 [Schedule]',
      },
      {
        key: 'table_1_6',
        label: 'Table 1.6 [Đừng vào]',
      },
      {
        key: 'cu_test',
        label: 'CU_TEST',
      },
      {
        key: 'table_1_5',
        label: 'Table 1.5 đừng edit',
      },
      {
        key: 'table_new_1_4',
        label: 'Table new 1.4[Đừng vào]',
      },
      {
        key: 'table_1_3',
        label: 'Table 1.3 [đừng vào]',
      },
      {
        key: 'table_new_1_2',
        label: 'Table new 1.2[Đừng vào]',
      },
      {
        key: 'table_xem_left_panel',
        label: 'table xem left panel',
      },
      {
        key: 'table_data_10_dung_vao',
        label: 'Table data 1.0 [Đừng vào]',
      },
      {
        key: 'table_30',
        label: 'Table 3.0.1',
      },
      {
        key: 'table_test_05',
        label: 'Table test 05',
      },
      {
        key: 'table_12',
        label: 'Table 1.2 đừng edit1.0',
      },
      {
        key: 'bushtin_4',
        label: 'BushTin_4',
      },
      {
        key: 'table_1231212',
        label: 'Table 1231212',
      },
      {
        key: 'cdp_test',
        label: 'cdp test',
      },
      {
        key: 'bushtin_1',
        label: 'BushTin_1',
      },
      {
        key: 'table_test_23',
        label: 'Table test 23',
      },
      {
        key: 'table_new_02',
        label: 'table new 02',
      },
      {
        key: 'phuoc_test',
        label: 'PHƯỚC TEST',
      },
      {
        key: 'table_check_long_name',
        label: 'Table check long name',
      },
      {
        key: 'sang_test_4',
        label: 'Sang test 4',
      },
      {
        key: 'sang_test_3',
        label: 'Sang test 3',
      },
      {
        key: 'table_22',
        label: 'Table 22',
      },
      {
        key: 'table_test_schedule_20',
        label: 'table test schedule 2.0',
      },
      {
        key: 'table_test_schedule_10',
        label: 'Table test schedule 1.0',
      },
      {
        key: 'table_d_xem_dung_edit',
        label: 'table D xem [Đừng edit]',
      },
      {
        key: 'amy_1',
        label: 'amy 1',
      },
      {
        key: 'table_123_123213',
        label: 'Table 123 123213',
      },
      {
        key: 'table_new_1',
        label: 'Table new 1',
      },
      {
        key: 'table_test_01',
        label: 'Table 1.1',
      },
      {
        key: 'table_test_321',
        label: 'TABLE QC TEST',
      },
      {
        key: 'table_qc_test',
        label: 'table qc test 1',
      },
      {
        key: 'table_test_32',
        label: 'Table test 32',
      },
      {
        key: 'table_123',
        label: 'Table 123',
      },
      {
        key: 'table_unit_test_2',
        label: 'Table unit test 231',
      },
    ],
  },
];
