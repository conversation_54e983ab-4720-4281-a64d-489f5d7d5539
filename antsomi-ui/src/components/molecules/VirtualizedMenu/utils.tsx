import { antsomiClsx } from '@antscorp/antsomi-ui/es/utils';
import { ItemType, PathKey, SerializedItemType } from './types';
import { COMPONENT_NAME, MODE } from './config';
import { memoize } from 'lodash';

export const getFlattenItems = (args: { items: ItemType[] }) => {
  const { items } = args;

  const result: SerializedItemType[] = [];

  function recursively(
    items: ItemType[],
    level: number,
    pathKey: PathKey,
    parent: ItemType | null,
  ): void {
    items.forEach((item, idx) => {
      const currentPathKey = [...pathKey, idx, item.key.toString()];

      result.push({ ...item, level, pathKey: currentPathKey, parent });

      if (item.children && item.children.length > 0) {
        recursively(item.children, level + 1, currentPathKey, item);
      }
    });
  }

  if (items.length) {
    recursively(items, 1, [], null);
  }

  return result;
};

const componentCls = antsomiClsx(COMPONENT_NAME);

export const CLS = {
  Root: {
    default: componentCls('menu-root'),
  },

  Menu: {
    default: componentCls('menu'),
  },

  MenuInline: {
    default: componentCls(`menu-${MODE.Inline}`),
  },

  ItemLabel: {
    default: componentCls('item-label'),
  },

  ItemAction: {
    default: componentCls('item-action'),
  },

  IconExpand: {
    default: componentCls('icon-expand'),
  },

  Item: {
    default: componentCls('item'),
    disabled: componentCls('item-disabled'),
    selected: componentCls('item-selected'),
    expanded: componentCls('item-expanded'),
  },
} as const;

export const getItemSize = memoize((itemSize: number, itemSpacing: number) => (index: number) => {
  if (index === 0) {
    return itemSize;
  }

  return itemSize + itemSpacing;
});

export const calInlineListSize = (itemLength: number, itemSize: number, itemSpacing: number) => {
  let result: number = 0;

  const calItemSize = getItemSize(itemSize, itemSpacing);

  for (let i = 0; i < itemLength; i++) {
    result += calItemSize(i);
  }

  return result;
};
