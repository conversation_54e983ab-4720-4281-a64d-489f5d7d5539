import styled from 'styled-components';
import { VariableSizeList } from 'react-window';
import { THEME, globalToken } from '@antscorp/antsomi-ui/es/constants';
import { MemoryData } from './components/Item/Item';
import AutoSizer from 'react-virtualized-auto-sizer';
import { CLS } from './utils';

export const MenuItemWrapper = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  gap: 8px;
  justify-content: space-between;
  padding: 5px;
  align-items: center;
  overflow: hidden;

  .icon-expand {
    font-size: 14px;
    flex-shirnk: 0;
  }

  .title-wrapper {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
`;

export const MenuItemRoot = styled.div`
  &.${CLS.Item.default} {
    cursor: pointer;
    display: flex;
    align-items: center;
    overflow: hidden;
    border-radius: ${THEME.components?.Menu?.itemBorderRadius}px;

    > .${CLS.ItemLabel.default} {
      flex: 1;
      overflow: hidden;
    }

    > .${CLS.ItemAction.default} {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &.${CLS.Item.disabled} {
      color: ${THEME.components?.Menu?.itemDisabledColor};
      cursor: not-allowed;
    }

    &.${CLS.Item.selected} {
      background-color: ${THEME.components?.Menu?.itemActiveBg};
    }

    &.${CLS.Item.expanded} {
      > .${CLS.ItemAction.default} {
        > .${CLS.IconExpand.default} {
          rotate: 180deg;
          transition: rotate 0.4s;
        }
      }
    }

    &:hover:not(.${CLS.Item.disabled}, .${CLS.Item.selected}) {
      background-color: ${THEME.components?.Menu?.itemHoverBg};
    }
  }
`;

export const VirtualizedMenuContainer = styled(VariableSizeList<MemoryData>)`
  font-size: ${globalToken?.fontSize}px;
  scrollbar-gutter: stable;
`;

export const StyledAutoSizer = styled(AutoSizer)`
  flex: 1;
`;
