import { PopoverProps } from 'antd';
import { InputProps } from '../../atoms/Input';
import React, { PropsWithChildren } from 'react';
import { MenuInlineProps } from '../VirtualizedMenu/types';

export type SearchConfig = {
  searchWithKey?: boolean;
};

export type SearchPopoverProps = PropsWithChildren<
  {
    inputSearchProps?: InputProps & { searchConfig?: SearchConfig };
    isAllowEmpty?: boolean;
  } & PopoverProps
>;

export type Option = {
  key: string;
  label: React.ReactNode | ((info: { selected: boolean }) => React.ReactNode);
  search?: string;
};

export type Field = Option & {
  dataType?: string | number;
};

export type PopoverSelectProps<TOption extends Option = Option> = SearchPopoverProps &
  Partial<{
    options: TOption[] | ((info: { selected: string[] }) => TOption[]);
    selected: string[];
    children: React.ReactNode;
    showAllLabel: string;
    showSelectedLabel: string;
    deselectAllLabel: string;
    selectAllLabel: string;
    menuProps: Pick<
      MenuInlineProps,
      'onClick' | 'inlineIndent' | 'inlinePadding' | 'itemSpacing' | 'className' | 'itemSize'
    >;
    inputValue?: boolean;
    onSearchPredicate: (option: Option) => boolean;
    onCancel: () => void;
    onApply: (selected: string[]) => void;
    onClickInputValue?: () => void;
  }>;

export type PopoverAddFieldProps = Omit<PopoverSelectProps, 'options'> & {
  fields: Field[];
};
