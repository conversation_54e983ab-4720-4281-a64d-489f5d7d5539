/* eslint-disable no-param-reassign */
// Libraries
import React, { memo, useCallback, useEffect, useMemo } from 'react';
import { useImmer } from 'use-immer';

// Styled & Components
import { CommonCollection, LineCollection, ViberCollection } from '../EmojiCollections';
import {
  EmojiBearIcon,
  EmojiLaughIcon,
  EmojiSmileIcon,
} from '@antscorp/antsomi-ui/es/components/icons';
import { Button } from '@antscorp/antsomi-ui/es/components/atoms';
import {
  EMOJI_COLLECTIONS,
  type TagDataCustomize,
  type EmojiCollection,
} from '@antscorp/antsomi-ui/es/components/molecules';
import { EmojiTabs, EmojiPopoverStyled } from './styled';

// Utils
import { handleGetTextToReplace } from './utils';

interface CollectionItemProps {
  key: EmojiCollection;
  label: React.ReactNode;
}

interface EmojiPopoverProps {
  disabled?: boolean;
  collections?: CollectionItemProps[];
  isForceHide?: boolean;
  children?: React.ReactNode;
  onEmojiClick?: (emoji: string | TagDataCustomize) => void;
}

const EmojiPopover: React.FC<EmojiPopoverProps> = ({
  disabled,
  collections,
  isForceHide,
  children,
  onEmojiClick,
}) => {
  const [state, setState] = useImmer<{ isOpen: boolean; collectionActive: EmojiCollection }>({
    isOpen: false,
    collectionActive: collections ? collections[0]?.key : EMOJI_COLLECTIONS.COMMON,
  });

  const handleClickCollection = useCallback(
    (newCollectionActive: string) => {
      setState(draft => {
        draft.collectionActive = newCollectionActive as EmojiCollection;
      });
    },
    [setState],
  );

  const handleGetCodeEmoji = useCallback(
    (newEmoji: string) => {
      setState(draft => {
        const newTag = handleGetTextToReplace(draft.collectionActive, newEmoji);

        if (typeof onEmojiClick === 'function') {
          onEmojiClick(newTag);
        }

        if (isForceHide) {
          draft.isOpen = false;
          draft.collectionActive = EMOJI_COLLECTIONS.COMMON;
        }
      });
    },
    [onEmojiClick, isForceHide, setState],
  );

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setState(draft => {
        draft.isOpen = newOpen;
      });
    },
    [setState],
  );

  useEffect(
    () => () => {
      setState(draft => {
        draft.isOpen = false;
        draft.collectionActive = EMOJI_COLLECTIONS.COMMON;
      });
    },
    [setState],
  );

  const renderCollection = useCallback(
    (collectionCurrent = '') => {
      switch (collectionCurrent) {
        case EMOJI_COLLECTIONS.VIBER: {
          return <ViberCollection onEmojiClick={handleGetCodeEmoji} />;
        }
        case EMOJI_COLLECTIONS.LINE: {
          return <LineCollection onEmojiClick={handleGetCodeEmoji} />;
        }
        default: {
          return <CommonCollection onEmojiClick={handleGetCodeEmoji} />;
        }
      }
    },
    [handleGetCodeEmoji],
  );

  const emojiCollections = useMemo(() => {
    if (!collections) return [];

    return collections.map((collection: Record<string, any>) => ({
      key: collection.key,
      label: collection.label,
      children: renderCollection(collection.key),
    }));
  }, [collections, renderCollection]);

  return (
    <EmojiPopoverStyled
      content={
        <EmojiTabs
          activeKey={state.collectionActive}
          onTabClick={handleClickCollection}
          items={emojiCollections}
        />
      }
      placement="topLeft"
      trigger="click"
      fresh={false}
      style={{ padding: 15 }}
      arrow={false}
      rootClassName="antsomi-emoji-popover"
      open={state.isOpen}
      onOpenChange={handleOpenChange}
    >
      {children || (
        <Button
          type="link"
          icon={<EmojiSmileIcon />}
          disabled={disabled}
          onClick={() => handleOpenChange(true)}
        />
      )}
    </EmojiPopoverStyled>
  );
};

EmojiPopover.defaultProps = {
  disabled: false,
  isForceHide: false,
  collections: [
    { key: EMOJI_COLLECTIONS.COMMON, label: <EmojiSmileIcon /> },
    { key: EMOJI_COLLECTIONS.VIBER, label: <EmojiLaughIcon /> },
    { key: EMOJI_COLLECTIONS.LINE, label: <EmojiBearIcon /> },
  ],
  onEmojiClick: () => {},
};

export default memo(EmojiPopover);
