// Libraries
import React, { ReactNode, useState } from 'react';
import AceEditor, { IAceEditorProps } from 'react-ace';

// Translations
import { translations } from '@antscorp/antsomi-ui/es/locales/translations';
import i18nInstance from '@antscorp/antsomi-ui/es/locales/i18n';

// Atoms
import { Button } from '@antscorp/antsomi-ui/es/components/atoms/Button';
import { Icon } from '@antscorp/antsomi-ui/es/components/atoms/Icon';

// Molecules
import { Modal } from '@antscorp/antsomi-ui/es/components/molecules/Modal';

// Styled
import { EditorScriptWrapper } from './styled';

// Ace editor
// import 'ace-builds/webpack-resolver';
import 'ace-builds/src-noconflict/mode-javascript';
import 'ace-builds/src-noconflict/mode-css';
import 'ace-builds/src-noconflict/theme-tomorrow';
import 'ace-builds/src-noconflict/ext-language_tools';

// Utils
import { handleError } from '@antscorp/antsomi-ui/es/utils';

export interface EditorScriptProps extends IAceEditorProps {
  label?: string | ReactNode;
  expandModalLabel?: string | ReactNode;
  hideExpand?: boolean | ReactNode;
}

const PATH = 'src/components/molecules/EditorScript/EditorScript.tsx';

export const EditorScript: React.FC<EditorScriptProps> = props => {
  // Props
  const { label, expandModalLabel, className, height, setOptions, hideExpand, ...restOf } = props;

  // State
  const [isExpandEditor, setExpandEditor] = useState(false);

  const { t } = i18nInstance;

  const renderAceEditor = (aceHeight = height) => {
    try {
      return (
        <AceEditor
          setOptions={{
            enableBasicAutocompletion: true,
            enableLiveAutocompletion: true,
            enableSnippets: true,
            showLineNumbers: true,
            tabSize: 2,
            ...setOptions,
          }}
          {...restOf}
          style={{
            width: '100%',
            height: aceHeight,
            ...restOf.style,
          }}
          onLoad={e => {}}
        />
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderAceEditor',
        args: {},
      });
    }
  };

  const onCancelModal = () => {
    try {
      setExpandEditor(false);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onCancelModal',
        args: {},
      });
    }
  };

  return (
    <>
      <EditorScriptWrapper className={className}>
        {label && <div className="ants-mb-5">{label}</div>}
        {renderAceEditor()}
        {!hideExpand && (
          <Button
            type="text"
            className="ants-mt-5px"
            onClick={() => {
              setExpandEditor(true);
            }}
          >
            <Icon
              type="icon-ants-expand"
              size={14}
              style={{
                transform: 'scaleX(-1)',
              }}
            />
            {t(translations.expandEditor.title) as string}
          </Button>
        )}
      </EditorScriptWrapper>
      <Modal
        visible={isExpandEditor}
        title={expandModalLabel}
        onCancel={onCancelModal}
        centered
        width={1000}
        footer={<Button onClick={onCancelModal}>{t(translations.close.title) as string}</Button>}
      >
        {renderAceEditor('500px')}
      </Modal>
    </>
  );
};

EditorScript.defaultProps = {
  expandModalLabel: 'Editor Script',
  mode: 'javascript',
  theme: 'tomorrow',
  name: 'Editor Script',
  placeholder: '',
  fontSize: 12,
  showPrintMargin: true,
  showGutter: true,
  highlightActiveLine: true,
  setOptions: {},
  hideExpand: false,
  height: '267px',
};
