import React, { forwardRef } from 'react';
import { IconProps } from './types';
import { useIcon } from './hooks/useIcon';

export const PenSparkIcon = forwardRef<SVGSVGElement, IconProps>((props, ref) => {
  const { width, height } = useIcon(props);
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
      ref={ref}
      width={width}
      height={height}
    >
      <path
        d="M21.4795 7.15625L20.2326 5.8975C20.0696 5.73091 19.8754 5.59864 19.6614 5.50836C19.4474 5.41809 19.2177 5.3716 18.9857 5.3716C18.7538 5.3716 18.5241 5.41809 18.3101 5.50836C18.096 5.59864 17.9019 5.73091 17.7389 5.8975L5.52889 18.2237V22H9.26952L21.4795 9.67375C22.1735 8.985 22.1735 7.85687 21.4795 7.15625ZM8.54021 20.2188H7.29334V18.96L16.492 9.67375L17.7389 10.9325L8.54021 20.2188ZM12.5867 8.34375C9.6577 8.34375 7.29334 5.95688 7.29334 3C7.29334 5.95688 4.92898 8.34375 2 8.34375C4.92898 8.34375 7.29334 10.7306 7.29334 13.6875C7.29334 10.7306 9.6577 8.34375 12.5867 8.34375Z"
        fill="currentColor"
      />
    </svg>
  );
});
