import React, { forwardRef } from 'react';
import { IconProps } from './types';
import { useIcon } from './hooks/useIcon';

export const NavigateNextIcon = forwardRef<SVGSVGElement, IconProps>((props, ref) => {
  const { width, height } = useIcon(props);
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
      ref={ref}
      width={width}
      height={height}
    >
      <path d="M10 6L8.59 7.41L13.17 12L8.59 16.59L10 18L16 12L10 6Z" fill="currentColor" />
    </svg>
  );
});
