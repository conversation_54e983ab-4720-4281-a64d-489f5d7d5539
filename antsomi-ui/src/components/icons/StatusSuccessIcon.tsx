import React, { forwardRef } from 'react';
import { IconProps } from './types';
import { useIcon } from './hooks/useIcon';

export const StatusSuccessIcon = forwardRef<SVGSVGElement, IconProps>((props, ref) => {
  const { width, height } = useIcon(props);
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
      ref={ref}
      width={width}
      height={height}
    >
      <path
        d="M12 2C6.42857 2 2 6.42857 2 12C2 17.5714 6.42857 22 12 22C17.5714 22 22 17.5714 22 12C22 6.42857 17.5714 2 12 2ZM10 17L5 12L6.42857 10.5714L10 14.1429L17.5714 6.57143L19 8L10 17Z"
        fill="currentColor"
      />
    </svg>
  );
});
