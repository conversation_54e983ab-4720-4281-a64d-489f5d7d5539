// Libraries
import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { Button, Flex, Typography } from 'antd';
import { get } from 'lodash';
import { isAndroid, isMobile } from 'react-device-detect';

// Components
import { WidgetLayout } from '../WidgetLayout';
import { Avatar } from '../Avatar';
import { IconInfo } from '../IconInfo';

import imageStep1 from '../../../../../assets/images/components/Login/step1.png';
import imageStep2 from '../../../../../assets/images/components/Login/step2.png';

// Styled
import { LabelStep, Link } from './styled';
import { StyleButton, StyleInput, Text } from '../../styled';

// Types
import { LoginData } from '../../types';

const { Text: TextAntsomiUI } = Typography;
interface SetupGGAuthenticatorProps {
  domainLogin: string;
  token?: string;
  userId: string;
  loginData: Partial<LoginData>;
  onSkipSetupGGAuthenticator?: () => void;
  onSetupGGAuthenticatorDone?: (data: any) => void;
  backLogin: () => void;
  loadingSkip: boolean;
}

const SetupGGAuthenticator: React.FC<SetupGGAuthenticatorProps> = props => {
  const {
    domainLogin,
    token = '',
    userId,
    loginData,
    onSkipSetupGGAuthenticator,
    onSetupGGAuthenticatorDone,
    backLogin,
    loadingSkip,
  } = props;

  const [codeError, setCodeError] = useState<boolean>(true);
  const [count, setCount] = useState<number>(0);
  const [isLoading, setLoading] = useState<boolean>(false);
  const [qrCode, setQRCode] = useState<string>('');
  const [g2faCode, setG2faCode] = useState<string>('');
  const errorRef = useRef<boolean>(false);

  useEffect(() => {
    getQRCode();
  }, []);

  const updateG2FA = () => {
    errorRef.current = true;
    setCount(prev => prev + 1);

    if (!token || !userId || codeError) {
      return;
    }

    const verifyParam = {
      g2fa: 1,
      g2fa_code: g2faCode,
    };

    const url = `${domainLogin}api/account/index/${userId}?_token=${token}&_user_id=${userId}&_account_id=${userId}`;

    setLoading(true);

    const xhr = new XMLHttpRequest();
    xhr.open('PUT', url, true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    xhr.withCredentials = false;

    xhr.onreadystatechange = function () {
      if (xhr.readyState === XMLHttpRequest.DONE) {
        setLoading(false);
      }
      if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
        const res = JSON.parse(xhr.responseText);
        if (res && res.code === 200 && res.data && res.data.userId) {
          if (onSetupGGAuthenticatorDone && typeof onSetupGGAuthenticatorDone === 'function') {
            onSetupGGAuthenticatorDone(res.data);
          }
        } else {
          setCodeError(true);
        }
      }
    };
    xhr.send(JSON.stringify(verifyParam));
  };

  const getQRCode = () => {
    const { personal, token } = loginData;
    const { user_id: userId } = personal || {};
    axios
      .get(
        `${domainLogin}api/account/info?_token=${token}&_user_id=${userId}&_account_id=${userId}&type=get-qr-code`,
      )
      .then(res => {
        if (res && res.data && res.data.data) {
          setQRCode(res.data.data);
        }
      });
  };

  const handleBack = () => {
    if (backLogin && typeof backLogin === 'function') {
      backLogin();
    }
  };

  return (
    <WidgetLayout
      title="Enable two-step authentication"
      isShowLogo={false}
      header={
        <Avatar
          image={get(loginData, 'personal.avatar')}
          name={get(loginData, 'personal.full_name')}
          email={get(loginData, 'personal.email')}
        />
      }
      content={
        <Flex vertical gap={30}>
          <Flex vertical gap={13}>
            <LabelStep isMobile={isMobile}>STEP 1</LabelStep>
            <Flex align="center" gap={5}>
              <Text isMobile={isMobile} color="#595959">
                {isMobile ? (
                  <span>
                    <Link
                      href={
                        isAndroid
                          ? 'https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2'
                          : 'https://apps.apple.com/us/app/google-authenticator/id388497605 '
                      }
                      target="_blank"
                      rel="noreferrer"
                    >
                      Install Google Authenticator
                    </Link>
                    & scan the code
                  </span>
                ) : (
                  'Install Google Authenticator on your phone & scan the code'
                )}
              </Text>
              <IconInfo
                image={imageStep1}
                title={
                  <TextAntsomiUI
                    style={{
                      marginTop: 20,
                    }}
                  >
                    <Text isMobile={isMobile} style={{ textAlign: 'center' }}>
                      Open <b>Google Authenticator</b> app on your phone and scan the QR code.
                    </Text>
                  </TextAntsomiUI>
                }
              />
            </Flex>

            {qrCode ? (
              <Flex
                vertical
                align="center"
                style={{
                  background: '#EEF5FC',
                  padding: 10,
                  borderRadius: 10,
                }}
              >
                <img
                  src={`data:image/gif;base64,${qrCode}`}
                  alt="QR Code"
                  height={160}
                  style={{
                    borderRadius: 10,
                  }}
                />
              </Flex>
            ) : null}
          </Flex>

          <Flex vertical gap={13}>
            <LabelStep isMobile={isMobile}>STEP 2</LabelStep>
            <Flex align="center" gap={5}>
              <Text isMobile={isMobile} color="#595959">
                Enter the 6-digit code from <span style={{ fontWeight: 'bold' }}>antsomi.com</span>
              </Text>
              <IconInfo
                image={imageStep2}
                title={
                  <TextAntsomiUI
                    style={{
                      marginTop: 20,
                    }}
                  >
                    <Text isMobile={isMobile}>
                      Enter the code to confirm the connection to this account
                    </Text>
                  </TextAntsomiUI>
                }
              />
            </Flex>

            <StyleInput
              isMobile={isMobile}
              placeholder="Input your code"
              onChange={e => {
                setG2faCode(e.target.value.trim());
                if (e.target.value.trim().length) {
                  setCodeError(false);
                }
              }}
              status={errorRef.current && codeError ? 'error' : ''}
            />
            {errorRef.current && codeError ? (
              <TextAntsomiUI type="danger">Invalid code, please try again</TextAntsomiUI>
            ) : null}
          </Flex>
        </Flex>
      }
      footer={
        <Flex vertical gap={92}>
          <Flex vertical gap={isMobile ? 30 : 15}>
            <StyleButton
              isMobile={isMobile}
              loading={isLoading}
              type="primary"
              onClick={updateG2FA}
            >
              Complete
            </StyleButton>
            <StyleButton
              isMobile={isMobile}
              type="text"
              onClick={onSkipSetupGGAuthenticator}
              loading={loadingSkip}
            >
              Skip
            </StyleButton>
          </Flex>
          <Flex justify="center">
            <Button
              type="text"
              onClick={handleBack}
              style={{
                fontSize: isMobile ? '16px' : '14px',
              }}
            >
              Sign in with a different account
            </Button>
          </Flex>
        </Flex>
      }
    />
  );
};

export default SetupGGAuthenticator;
