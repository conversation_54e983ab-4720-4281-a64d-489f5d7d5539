/* eslint-disable func-names */
/* eslint-disable no-empty */
/* eslint-disable no-return-assign */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable no-prototype-builtins */
/* eslint-disable react-hooks/exhaustive-deps */

/// Libraries
import React, { useState, useEffect, useRef, useMemo } from 'react';
import { CookiesProvider, useCookies } from 'react-cookie';
import { get } from 'lodash';
import axios from 'axios';
import { isMobile } from 'react-device-detect';
import { useSearchParams } from 'react-router-dom';

// Components
import {
  LoginSelectPortal,
  LoginStep2,
  SignIn,
  VerifyAccount,
  SetupGGAuthenticator,
  ForgotPassword,
} from './components';

// Hooks
import { useContainerDimensions } from './hooks';

// Utils
import { checkURL, navigateTo } from './utils';
import { Account, LoginComponentProps, LoginData, NetworkInfo } from './types';
import { STEPS } from './constants';

import { LoginBannerZone, LoginMain, WrapperLogin } from './styled';

export const Login: React.FC<LoginComponentProps> = props => {
  const {
    loginDomain = 'http://khanhhv.platform-ants.adxdev.vn',
    onLoginSuccess,
    onLoginFail,
    reCaptchaKey = '6LfqGF0UAAAAAFJri0tlp8eCNbWZdJv1eAyF4oIo',
    p_id,
    onResendPassFail,
    u_ogs,
    callbackUrl,
    apiDomain = '',
    usePrivilege,
  } = props;

  let { networkId } = props;
  networkId = !networkId || typeof networkId === 'boolean' ? undefined : networkId;

  const domainLogin = loginDomain.endsWith('/') ? loginDomain : `${loginDomain}/`;
  const loginUrl = `${domainLogin}api/account/authenticate`;

  const [step, setStep] = useState<number>(STEPS.STEP1);
  const [loginData, setLoginData] = useState<Partial<LoginData>>({});
  const [isLoginGG, setIsLoginGG] = useState<boolean>(false);
  const [urlLogo, setUrlLogo] = useState<string>('');
  const [isLoading, setLoading] = useState<boolean>(false);
  const [isLoadingSkip, setLoadingSkip] = useState<boolean>(false);
  const [cookies, setCookie] = useCookies();

  const selectedNetworkRef = useRef<NetworkInfo | null>(null);
  const listNetworkRef = useRef<NetworkInfo[] | null>(null);
  const listAccountsRef = useRef<Account[] | null>(null);
  const authenInfoRef = useRef<{ email: string } | null>(null);
  const loginRef = useRef<HTMLElement>(null);

  const searchParams = new URLSearchParams(window.location.search);

  useEffect(() => {
    if (searchParams.get('ui') === 'forgot') {
      setStep(STEPS.STEP_FORGOT);

      searchParams.delete('ui');

      const newSearch = searchParams.toString();
      history.pushState({}, '', `?${newSearch}`);
    }
  }, [searchParams]);

  const getObjectPropSafely = (fn: () => {}, defaultValue) => {
    try {
      return fn();
    } catch (e) {
      return defaultValue;
    }
  };

  const getMenuUrl = (menu: any, userId = ''): string => {
    if (!menu || !menu.menu_path) {
      return '';
    }
    const { app_domain: appDomain, app_path: appPath, menu_path: menuPath } = menu;
    return (appDomain + appPath + menuPath).replace(/:user_id/g, userId);
  };

  const getNetworkInfo = (app_id: string) =>
    fetch(`${domainLogin}api/network/info?app_id=${app_id}`)
      .then(response => response.json())
      .then(res => {
        if (res?.data?.networkInfo?.logo) {
          setUrlLogo(res.data.networkInfo.logo);
        }
        if (res?.data?.use_google_login && +res.data.use_google_login === 1) {
          setIsLoginGG(true);
        }
      });

  useEffect(() => {
    if (networkId) {
      getNetworkInfo(networkId as string);
    }
  }, [networkId]);

  const getAppsMenus = ({ token, userId, accountId }: any, callback: (res: any) => void) => {
    if (apiDomain && token && userId && accountId) {
      const apiUrl = `${apiDomain}${usePrivilege ? '/api/privilege/index' : '/api/permission/index'}`;

      axios({
        url: apiUrl,
        params: {
          type: 'list-app',
          _token: token,
          _user_id: userId,
          _account_id: accountId,
          _lang: 'en',
          hasChild: true,
          from: 'login',
        },
      }).then(res => {
        callback(res);
      });
    }
  };

  const getListAccountSharing = ({ token, userId, accountId }: any) => {
    const params = {
      type: 'get-list-access',
      _token: token,
      _user_id: userId,
      _account_id: accountId,
      _lang: 'en',
      limit: 1000,
      search: '',
    };
    return axios.get(`${apiDomain}/api/account/share-access`, { params });
  };

  const redirectFirstMenuAccount = async (redirectAccountId: string, token?: string) => {
    if (!redirectAccountId) {
      return;
    }
    axios
      .get(`${apiDomain}/api/privilege/index`, {
        params: {
          _token: token,
          _user_id: redirectAccountId,
          _account_id: redirectAccountId,
          _lang: 'en',
          hasChild: true,
          type: 'list-app',
          from: 'header',
        },
      })
      .then(res => {
        if (res && res.data) {
          redirectFirstMenu(res.data.data, redirectAccountId);
        }
      });
  };

  const redirectFirstMenu = (data: any, redirectAccountId?: string) => {
    if (!Array.isArray(data)) {
      return false;
    }

    const arrMenus = getObjectPropSafely(
      () => [...data].sort((first, second) => first.app_order - second.app_order)[0].childs,
      [],
    );

    if (arrMenus.length) {
      const firstMenu = arrMenus
        .filter(menu => +menu.show_hide)
        .sort((first, second) => first.menu_order - second.menu_order)[0];

      if (firstMenu) {
        const redirectUrl = getMenuUrl(firstMenu, redirectAccountId);

        navigateTo(redirectUrl);
      }
    }
  };

  const checkRedirectHomePage = (homePage?: string, data?: any, userId?: string) => {
    const convertedHomePage = String(homePage).replace(/:user_id/g, userId || '');
    if (!Array.isArray(data) || !data.length) {
      return navigateTo(convertedHomePage);
    }
    const arrMenus = buildListAppMenus(data, userId);

    if (!arrMenus.some(url => convertedHomePage.includes(url))) {
      return redirectFirstMenu(data, userId);
    }

    navigateTo(convertedHomePage);
  };

  const buildListAppMenus = (data: any[], userId?: string): string[] => {
    if (!Array.isArray(data) || !data.length) {
      return [];
    }

    let arrMenus: string[] = [];

    data.forEach(item => {
      if (item.menu_id) {
        arrMenus.push(getMenuUrl(item, userId));
      }

      if (item.childs && item.childs.length) {
        arrMenus = arrMenus.concat(buildListAppMenus(item.childs, userId));
      }
    });

    return arrMenus;
  };

  const handleLoginSuccess = (
    data: Partial<LoginData> | null = null,
    networkRedirect: any = null,
    accountRedirect: any = null,
  ) => {
    // check setup loading skip
    if (data && data.isCheckSkip) {
      setLoadingSkip(true);
    }

    if (data && data.isCheckG2FA && data.G2FA_user === 0) {
      setLoginData(data);
      setStep(STEPS.STEP_SETUP_GG_AUTHENTICATOR);
    } else if (data && data.G2FA === 1) {
      setLoginData(data);
      setStep(STEPS.STEP2);
    } else {
      const dataLogin = data || loginData;

      if (accountRedirect) {
        return redirectFirstMenuAccount(accountRedirect.user_id, dataLogin.token);
      }

      let isCallbackUrl = false;
      let isHomePageUrl = false;

      if (callbackUrl && checkURL(callbackUrl)) {
        isCallbackUrl = true;
      }
      if (selectedNetworkRef.current && selectedNetworkRef.current.homePage) {
        isHomePageUrl = true;
      }

      if (networkId || selectedNetworkRef.current) {
        if (
          (onLoginSuccess && typeof onLoginSuccess === 'function') ||
          isCallbackUrl ||
          isHomePageUrl ||
          networkRedirect ||
          selectedNetworkRef.current
        ) {
          if (apiDomain) {
            getAppsMenus(
              {
                token: dataLogin.token,
                userId: dataLogin?.personal?.user_id,
                accountId: dataLogin?.personal?.user_id,
              },
              res => {
                const menus = getObjectPropSafely(() => res.data.data, []);

                if (menus && menus.length) {
                  handleLoginData(
                    dataLogin,
                    !isCallbackUrl && !isHomePageUrl,
                    selectedNetworkRef.current && selectedNetworkRef.current.networkId,
                  );

                  if (onLoginSuccess && typeof onLoginSuccess === 'function') {
                    onLoginSuccess(dataLogin);
                    setLoadingSkip(false);
                  } else if (isCallbackUrl && callbackUrl) {
                    navigateTo(callbackUrl);
                    setLoadingSkip(false);
                  } else if (isHomePageUrl) {
                    checkRedirectHomePage(
                      selectedNetworkRef.current?.homePage,
                      menus,
                      dataLogin.personal?.user_id,
                    );
                    setLoadingSkip(false);
                  }
                } else {
                  // Get user share
                  getListAccountSharing({
                    token: dataLogin.token,
                    userId: dataLogin.personal?.user_id,
                    accountId: dataLogin.personal?.user_id,
                  }).then(res => {
                    if (
                      getObjectPropSafely(() => res.data.data.list_access.length, 0) &&
                      res.data.data.list_access.some(
                        account =>
                          account.user_id.toString() !== dataLogin.personal?.user_id.toString(),
                      )
                    ) {
                      handleLoginData(
                        dataLogin,
                        false,
                        selectedNetworkRef.current && selectedNetworkRef.current.networkId,
                      );
                      if (data) {
                        setLoginData(data);
                      }
                      listAccountsRef.current = getObjectPropSafely(
                        () =>
                          res.data.data.list_access.filter(
                            account =>
                              account.user_id.toString() !== dataLogin.personal?.user_id.toString(),
                          ),
                        0,
                      );
                      setStep(STEPS.STEP_SELECT_ACCOUNT);
                      setLoadingSkip(false);
                    } else {
                      // No menu, no share access, just redirect to home page
                      handleLoginData(
                        dataLogin,
                        !isCallbackUrl && !isHomePageUrl,
                        selectedNetworkRef.current && selectedNetworkRef.current.networkId,
                      );

                      setLoadingSkip(false);

                      if (onLoginSuccess && typeof onLoginSuccess === 'function') {
                        onLoginSuccess(dataLogin);
                      } else if (isCallbackUrl && callbackUrl) {
                        navigateTo(callbackUrl);
                      } else if (isHomePageUrl) {
                        navigateTo(selectedNetworkRef.current?.homePage);
                      }
                    }
                  });
                  // if (res && res.data && res.data.data && res.data.data.length) {
                  //   const appData = res.data.data
                  //   setApps(appData)
                  // }
                }
              },
            );
          } else {
            setLoadingSkip(false);

            handleLoginData(
              dataLogin,
              !isCallbackUrl && !isHomePageUrl,
              selectedNetworkRef.current && selectedNetworkRef.current.networkId,
            );

            if (onLoginSuccess && typeof onLoginSuccess === 'function') {
              onLoginSuccess(dataLogin);
            } else if (isCallbackUrl && callbackUrl) {
              navigateTo(callbackUrl);
            } else if (isHomePageUrl) {
              navigateTo(selectedNetworkRef.current?.homePage);
            }
          }
        } else {
          setLoadingSkip(false);

          if (data) {
            setLoginData(data);
          }
          getListNetworkSelect(dataLogin.token, networkId);
        }
      } else {
        setLoadingSkip(false);

        if (data) {
          setLoginData(data);
        }
        getListNetworkSelect(dataLogin.token);
      }
    }
  };

  const loginNetworkWithToken = async (
    token: string,
    networkItem: NetworkInfo,
    networkRedirect: any = null,
  ) => {
    if (!token || !networkItem || !networkItem.networkId) {
      return;
    }
    selectedNetworkRef.current = networkItem;
    const { networkId: networkIdSelected } = networkItem;
    const data = {
      token,
      app_id: networkIdSelected,
    };

    setLoading(true);
    const xhr = new XMLHttpRequest();
    xhr.open('POST', loginUrl, true);

    xhr.setRequestHeader('Content-Type', 'application/json');
    xhr.withCredentials = false;

    xhr.onreadystatechange = () => {
      setLoading(false);

      if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
        const response = JSON.parse(xhr.responseText);

        if (response?.data?.token) {
          handleLoginSuccess(response.data, networkRedirect);
        } else {
          handleLoginFail();
        }
      }
    };
    xhr.send(JSON.stringify(data));
  };

  const getListNetworkSelect = (token?: string, networkRedirect: string = '') => {
    if (selectedNetworkRef.current && selectedNetworkRef.current.networkId) {
      return;
    }
    if (token) {
      setLoading(true);
      fetch(`${domainLogin}api/network/info?type=get-list-network&token=${token}`)
        .then(response => response.json())
        .then(res => {
          if (res?.data?.length) {
            listNetworkRef.current = res.data.map((network: any) => ({
              networkId: network.network_id,
              networkName: network.network_name,
              homePage: network.home_page,
              logo: network.logo,
            }));

            if (!res.data.length) {
              return;
            }

            if (networkRedirect) {
              const redirectNetwork = res.data.find(
                (network: any) => +network.network_id === +networkRedirect,
              );

              if (redirectNetwork) {
                loginNetworkWithToken(
                  token,
                  {
                    networkId: redirectNetwork.network_id,
                    networkName: redirectNetwork.network_name,
                    homePage: redirectNetwork.home_page,
                    logo: redirectNetwork.logo,
                  },
                  networkRedirect,
                );
              } else {
                if (res.data.length > 1) {
                  setStep(STEPS.STEP_SELECT_NETWORK);
                } else {
                  loginNetworkWithToken(token, {
                    networkId: res.data[0].network_id,
                    networkName: res.data[0].network_name,
                    homePage: res.data[0].home_page,
                    logo: res.data[0].logo,
                  });
                }
              }
            } else {
              if (res.data.length > 1) {
                setStep(STEPS.STEP_SELECT_NETWORK);
              } else {
                loginNetworkWithToken(token, {
                  networkId: res.data[0].network_id,
                  networkName: res.data[0].network_name,
                  homePage: res.data[0].home_page,
                  logo: res.data[0].logo,
                });
              }
            }
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  const callbackSelectNetwork = (networkItem: NetworkInfo) => {
    loginNetworkWithToken(loginData.token!, networkItem);
  };

  const handleLoginFail = (data?: any) => {
    if (data?.code) {
      switch (data.code) {
        case 106:
          setStep(STEPS.STEP_VERIFY_ACCOUNT);
          break;
        default:
          break;
      }
    }
    if (onLoginFail && typeof onLoginFail === 'function') {
      onLoginFail(data);
    }
  };

  const onSetupGGAuthenticatorDone = () => {
    setStep(STEPS.STEP2);
  };

  const onSkipSetupGGAuthenticator = () => {
    handleLoginSuccess({
      ...(loginData || {}),
      isCheckG2FA: false,
      isCheckSkip: true,
    });
  };

  const onForgotPassword = () => {
    setStep(STEPS.STEP_FORGOT);
  };

  const onResendSuccess = () => {
    setStep(STEPS.STEP_FORGOT_RESEND);
  };

  const backLogin = () => {
    selectedNetworkRef.current = null;
    setLoginData({});
    authenInfoRef.current = null;
    setStep(STEPS.STEP1);
  };

  const setAuthenInfo = (info: { email: string }) => (authenInfoRef.current = info);

  // eslint-disable-next-line @typescript-eslint/dot-notation
  window['popupCallbackLoginUser'] = function (data: any) {
    postMessage(data);
  };

  const postMessage = (postParam: any, doPostEvent: string | boolean = true) => {
    if (postParam.error && postParam.error !== '') {
    } else {
      const arrTmp = window.location.hostname.split('.');
      if (postParam.action === 'ogs-authenticate') {
        const loginData = { ...postParam, type: 'all_account' };

        if (loginData.hasOwnProperty('full_name')) {
          delete loginData.full_name;
        }
        if (loginData.hasOwnProperty('email')) {
          delete loginData.email;
        }
        if (loginData.hasOwnProperty('avatar')) {
          delete loginData.avatar;
        }
        if (loginData.hasOwnProperty('action')) {
          delete loginData.action;
        }
        if (loginData.hasOwnProperty('env')) {
          delete loginData.env;
        }

        if (u_ogs && typeof u_ogs === 'string' && u_ogs.trim()) {
          const replaceNetwork = getObjectPropSafely(
            () => selectedNetworkRef.current?.networkId || 0,
            networkId,
          );
          const cookieKey = u_ogs.includes(replaceNetwork)
            ? u_ogs
            : `${u_ogs}_${selectedNetworkRef.current?.networkId}`;
          const maxAge = 30 * 24 * 3600;

          if (window.location.protocol !== 'https:') {
            if (arrTmp.length < 4) {
              setCookie(cookieKey, JSON.stringify(loginData), {
                domain: arrTmp.slice(-2).join('.'),
                path: '/; samesite=None',
                maxAge,
              });
            } else {
              setCookie(cookieKey, JSON.stringify(loginData), {
                domain: arrTmp.slice(-3).join('.'),
                path: '/; samesite=None',
                maxAge,
              });
            }
          } else {
            if (arrTmp.length < 4) {
              setCookie(cookieKey, JSON.stringify(loginData), {
                domain: arrTmp.slice(-2).join('.'),
                path: '/; samesite=None; secure',
                maxAge,
              });
            } else {
              setCookie(cookieKey, JSON.stringify(loginData), {
                domain: arrTmp.slice(-3).join('.'),
                path: '/; samesite=None; secure',
                maxAge,
              });
            }
          }
        }
      }

      if (doPostEvent) {
        if (parent && parent.postMessage) {
          parent.postMessage(JSON.stringify(postParam), '*');
        } else {
          postMessage(JSON.stringify(postParam), '*');
        }
      }
    }
  };

  const handleLoginData = (
    data: Partial<LoginData> | null = null,
    doPostEvent = true,
    doSelectNetwork: number | null,
  ) => {
    data = data || loginData;

    if (!data.token) {
      return;
    }

    if (data && data.personal) {
      localStorage.setItem('user_logged_in_full_name', data.personal.full_name);
      localStorage.setItem('user_logged_in_avatar', data.personal.avatar);
      localStorage.setItem('user_logged_in_email', data.personal.email);
    }

    if (u_ogs && typeof u_ogs === 'string' && u_ogs.trim()) {
      const userData: Partial<LoginData['personal']> = {};
      Object.assign(userData, data.personal);

      if (userData.hasOwnProperty('full_name')) {
        delete userData.full_name;
      }
      if (userData.hasOwnProperty('email')) {
        delete userData.email;
      }
      if (userData.hasOwnProperty('avatar')) {
        delete userData.avatar;
      }
      if (userData.hasOwnProperty('action')) {
        delete userData.action;
      }
      userData.token = data.token;
    }

    const postParam = {
      action: 'ogs-authenticate',
      user_id: data?.personal?.user_id,
      account_id: data?.personal?.user_id,
      full_name: data?.personal?.full_name,
      role: data?.personal?.role,
      seller_role: data?.personal?.seller_role,
      conversion_id: data?.personal?.conversion_id,
      email: data?.personal?.email,
      avatar: data?.personal?.avatar,
      token: data?.token,
      language: data?.personal?.language,
    };

    postMessage(postParam, doPostEvent);

    postMessage(postParam, doPostEvent);
  };

  const handleSelectAccount = (account: Account) => {
    if (account && account.user_id) {
      handleLoginSuccess(null, null, account);
    }
  };

  const sendMailValidateAccount = async (callback: (data: any) => void) => {
    if (!authenInfoRef.current || !authenInfoRef.current.email) {
      return callback(null);
    }

    const loginUrl = `${domainLogin}account/resend-verify-account`;
    const { email } = authenInfoRef.current;

    const loginData = {
      user_name: email,
      app_id: networkId,
    };
    setLoading(true);

    const xhr = new XMLHttpRequest();
    xhr.open('POST', loginUrl, true);

    xhr.setRequestHeader('Content-Type', 'application/json');
    xhr.withCredentials = false;

    xhr.onreadystatechange = () => {
      setLoading(false);

      if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
        const response = JSON.parse(xhr.responseText);
        const { data } = response;

        if (callback && typeof callback === 'function') {
          callback(data || response);
        }
      }
    };
    xhr.send(JSON.stringify(loginData));
  };

  const { width } = useContainerDimensions(loginRef);

  const scaleCaptcha = useMemo(() => +((+width * 1.02) / 400).toFixed(2), [width]);

  const step1Config = {
    domainLogin,
    networkId,
    u_ogs,
    urlLogo,
    isLoginGG,
    callbackUrl,
    loadingNetwork: isLoading,
    handleLoginData,
    setAuthenInfo,
    onForgotPassword,
    onLoginSuccess: handleLoginSuccess,
    onLoginFail: handleLoginFail,
  };
  const step2Config = {
    domainLogin,
    token: loginData.token || '',
    username: get(loginData, 'personal.email') || '',
    userId: get(loginData, 'personal.user_id') || 0,
    urlLogo,
    loginData,
    handleLoginData,
    onLoginSuccess: handleLoginSuccess,
    backLogin,
    onLoginFail: handleLoginFail,
    onSetupGGAuthenticatorDone,
    onSkipSetupGGAuthenticator,
    loadingSkip: isLoadingSkip,
  };

  const forgotConfig = {
    domainLogin,
    networkId,
    reCaptchaKey,
    p_id,
    urlLogo,
    callbackUrl,
    onResendSuccess,
    backLogin,
    onResendFail: onResendPassFail,
    scaleCaptcha,
  };

  const stepSelectNetworkConfig = {
    domainLogin,
    networkId,
    u_ogs,
    urlLogo,
    isLoginGG,
    callbackUrl,
    loginData: {
      ...loginData,
      personal: loginData.personal || {
        avatar: '',
        full_name: '',
        email: '',
        user_id: '',
      },
    },
    listNetworks: listNetworkRef.current,
    callbackSelectNetwork,
    handleLoginData,
    backLogin,
    onForgotPassword,
    onLoginSuccess: handleLoginSuccess,
    onLoginFail: handleLoginFail,
  };

  const stepSelectAccountConfig = {
    loginData,
    urlLogo,
    listAccounts: listAccountsRef.current,
    handleSelectAccount,
  };

  const stepVerifyAccount = {
    domainLogin,
    networkId,
    u_ogs,
    urlLogo,
    isLoginGG,
    callbackUrl,
    loginData,
    listNetworks: listNetworkRef.current,
    callbackSelectNetwork,
    handleLoginData,
    onForgotPassword,
    onLoginSuccess: handleLoginSuccess,
    onLoginFail: handleLoginFail,
    authenInfo: authenInfoRef.current,
    backLogin,
    sendMailValidateAccount,
  };

  const renderMain = () => {
    switch (step) {
      case STEPS.STEP1:
        return <SignIn {...step1Config} />;

      case STEPS.STEP2:
        return <LoginStep2 {...step2Config} />;

      case STEPS.STEP_SELECT_NETWORK:
        return <LoginSelectPortal {...stepSelectNetworkConfig} />;

      case STEPS.STEP_SETUP_GG_AUTHENTICATOR:
        return <SetupGGAuthenticator {...step2Config} />;

      case STEPS.STEP_VERIFY_ACCOUNT:
        return <VerifyAccount {...stepVerifyAccount} />;

      default:
        return <ForgotPassword {...forgotConfig} />;
    }
  };

  return (
    <CookiesProvider>
      <LoginMain justify={isMobile ? 'center' : 'start'} id="login_container">
        <WrapperLogin
          isMobile={isMobile}
          vertical
          align="center"
          gap={40}
          flex={1}
          ref={loginRef}
          id="login_main"
        >
          {renderMain()}
        </WrapperLogin>
        {!isMobile && <LoginBannerZone className="login-banner-zone" id="login_banner_zone" />}
      </LoginMain>
    </CookiesProvider>
  );
};
