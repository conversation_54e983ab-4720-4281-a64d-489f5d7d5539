/* eslint-disable no-return-assign */
export const checkURL = url => {
  let result = true;

  try {
    // eslint-disable-next-line no-new
    new URL(url);
  } catch (e) {
    result = false;
  }

  return result;
};

/**
 * Executes the provided function and returns its result. If an error occurs,
 * the default value is returned instead.
 *
 * @param {Function} fn - The function to execute.
 * @param {any} [defaultValue=''] - The default value to return if an error occurs.
 * @return {any} The result of the function or the default value.
 */
export const getObjectPropSafely = (fn = () => {}, defaultValue = '') => {
  try {
    return fn();
  } catch (e) {
    return defaultValue;
  }
};

export const addAtmSourceLogin = url =>
  url ? `${url}${url.includes('?') ? '&' : '?'}atm_source=login` : url;

export const navigateTo = (url?: string) => (window.location.href = addAtmSourceLogin(url));

export const isUrl = url => {
  try {
    if (!url || !url.trim()) {
      return false;
    }
    return /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/.test(
      url,
    );
  } catch (e) {
    return false;
  }
};
