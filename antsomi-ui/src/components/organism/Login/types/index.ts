export interface NetworkInfo {
  networkId: number;
  networkName: string;
  homePage: string;
  logo: string;
}

export interface LoginData {
  token: string;
  personal: {
    full_name: string;
    email: string;
    avatar: string;
    user_id: string;
    role: string;
    language: string;
    [key: string]: any;
  };
  G2FA_user: number;
  G2FA: number;
  status: boolean;
  [key: string]: any;
}

export interface Account {
  user_id: number;
  [key: string]: any;
}

export interface LoginComponentProps {
  loginDomain: string;
  apiDomain?: string;
  networkId?: string | boolean;
  onLoginSuccess?: (data: Partial<LoginData>) => void;
  onLoginFail?: (data: any) => void;
  reCaptchaKey?: string;
  p_id?: string;
  onResendPassFail?: () => void;
  u_ogs?: string;
  width?: number;
  callbackUrl?: string;
  theme?: string;
  usePrivilege?: boolean;
}
