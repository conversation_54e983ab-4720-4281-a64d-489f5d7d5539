// Libraries
import { useEffect, useMemo } from 'react';
import { sortBy, uniqBy } from 'lodash';

// Queries
import {
  useGetDashboard,
  useGetDestinationChannel,
  useGetListMenu,
  useGetListMenuPermission,
} from '@antscorp/antsomi-ui/es/queries/LeftMenu';

// Stores
import { useLeftMenuContext } from '../contexts';

// Constants
import { CDP_API, PERMISSION_API, POST_MESSAGE_TYPES } from '@antscorp/antsomi-ui/es/constants';
import { ENV } from '@antscorp/antsomi-ui/es/config';

// Utils
import {
  findLastMatchedItemByUrl,
  findPathOfActiveItem,
  flattenMenuArray,
  getMappingAppChildren,
  recursivePermissionMenu,
} from '../utils';
import { TDashboard } from '@antscorp/antsomi-ui/es/models/LeftMenu';
import { useCheckUserPermission } from '@antscorp/antsomi-ui/es/queries';
import { recursiveSortBy } from '@antscorp/antsomi-ui/es/utils';
import { useDeepCompareEffect } from '@antscorp/antsomi-ui/es/hooks';

const { DEV, SANDBOX, SANDBOX_CDP } = ENV;

export const usePermission = () => {
  const appConfig = useLeftMenuContext(store => store.appConfig);
  const type = useLeftMenuContext(store => store.type);
  const setLeftMenuState = useLeftMenuContext(store => store.setState);
  const { isGrouped, objectId, objectType } = useLeftMenuContext(store => store.dashboardParams);
  const { auth, languageCode = 'en', env = 'development', permissionDomain } = appConfig || {};
  const { pathname, hash } = window.location;

  const { data: menuList } = useGetListMenu({
    args: {
      auth: {
        // token: '5474r2x214r2b4b4v2d4y444u2v5q2p5o4d4c4f424e4' || auth?.token,
        token: auth?.token,
        // url: 'https://sandbox-permission.ants.vn',
        url: PERMISSION_API, // NOTE: Hard for sandbox, development
        portalId: auth?.portalId,
        userId: auth?.userId,
        accountId: auth?.accountId,
      },
      params: {
        type: 'menu-item-permission',
      },
    },
    options: {
      enabled: !!auth?.userId && !!auth.portalId,
    },
  });

  const { data: userPermission } = useCheckUserPermission({
    apiConfig: {
      userId: +(auth?.userId || 0),
      accountId: +(auth?.accountId || 0),
      portalId: auth?.portalId,
      token: auth?.token,
      languageCode,
      domain: CDP_API?.[env],
    },
  });

  const { data: dashboardData, refetch: refetchDashboard } = useGetDashboard({
    args: {
      auth: {
        token: auth?.token,
        url: `${CDP_API?.[env]}/hub`,
        portalId: auth?.portalId,
        userId: auth?.userId,
      },
      params: {
        objectType,
        objectId,
        isGrouped,
        languageCode,
      },
    },
  });

  const { ownedDashboard = [], sharedDashboard = [] } = dashboardData?.entries || {};
  const dashboardList: TDashboard[] = useMemo(
    () => uniqBy([...ownedDashboard, ...sharedDashboard], 'dashboardId'),
    [ownedDashboard, sharedDashboard],
  );

  const { data: destinationChannelGen2 } = useGetDestinationChannel({
    args: {
      auth: {
        token: auth?.token,
        url: env ? `${CDP_API[env]}/hub` : '',
        userId: auth?.userId,
        portalId: auth?.portalId,
      },
      params: {
        objectType: 'GEN2_DESTINATION',
        languageCode,
      },
    },
  });

  const { data: destinationChannel } = useGetDestinationChannel({
    args: {
      auth: {
        token: auth?.token,
        url: env ? `${CDP_API[env]}/hub` : '',
        userId: auth?.userId,
        portalId: auth?.portalId,
      },
      params: {
        objectType: 'CHANNEL_INTERGRATION',
        languageCode,
      },
    },
  });

  const { data: menuListPermission } = useGetListMenuPermission({
    args: {
      auth: {
        token: auth?.token,
        url: permissionDomain || PERMISSION_API,
        // NOTE: Hot fix for case menu permission working wrong
        // userId: auth?.userId,
        userId: auth?.accountId,
        accountId: auth?.accountId,
      },
      params: {
        type: 'list-app',
        from: 'side',
        hasChild: true,
        languageCode,
      },
    },
  });

  const flattenMenuPermission = flattenMenuArray(menuListPermission || [], 'childs');

  const permissionMenu = useMemo(
    () =>
      recursivePermissionMenu(
        sortBy(menuList?.rows || [], ['level_position']),
        flattenMenuPermission,
      ),
    [menuList?.rows, flattenMenuPermission],
  );

  const mappingChildrenMenu = useMemo(
    () =>
      getMappingAppChildren({
        menuList: permissionMenu,
        auth,
        dashboardList,
        destinationChannelEntries: destinationChannel?.entries,
        destinationChannelGen2Entries: destinationChannelGen2?.entries,
        isRecommendation: type === 'recommendation',
        userPermission,
      }),
    [
      auth,
      dashboardList,
      destinationChannel?.entries,
      destinationChannelGen2?.entries,
      type,
      permissionMenu,
      userPermission,
    ],
  );

  const mappingChildrenMenuWithNoPermission = useMemo(
    () =>
      getMappingAppChildren({
        menuList: recursiveSortBy(menuList?.rows || [], 'children', ['level_position']) || [],
        auth,
        dashboardList,
        destinationChannelEntries: destinationChannel?.entries,
        destinationChannelGen2Entries: destinationChannelGen2?.entries,
        isRecommendation: type === 'recommendation',
        userPermission,
      }),
    [
      auth,
      dashboardList,
      destinationChannel?.entries,
      destinationChannelGen2?.entries,
      type,
      menuList?.rows,
      userPermission,
    ],
  );

  const activeItemPath = useMemo(() => {
    const url = `${pathname}${hash}`;

    const matchedCode = findLastMatchedItemByUrl({
      url,
      menuItems: mappingChildrenMenuWithNoPermission,
      auth,
      env,
    });

    return matchedCode
      ? findPathOfActiveItem({
          activeMenuItem: matchedCode,
          menuItems: mappingChildrenMenuWithNoPermission,
        })
      : [];
  }, [auth, env, hash, mappingChildrenMenuWithNoPermission, pathname]);

  useDeepCompareEffect(
    () => setLeftMenuState({ dashboardList }),
    [dashboardList, setLeftMenuState],
  );

  useEffect(() => {
    if (type !== 'customization') {
      const receivePostMessage = (event: MessageEvent) => {
        const { data } = event;
        const { type } = data || {};
        switch (type) {
          case POST_MESSAGE_TYPES.REFETCH_DASHBOARD_LIST: {
            refetchDashboard();
            break;
          }
          default:
            break;
        }
      };
      window.addEventListener('message', receivePostMessage);
      return () => window.removeEventListener('message', receivePostMessage);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    mappingChildrenMenu,
    permissionMenu,
    activeItemPath,
    menuListPermission,
    flattenMenuPermission,
  };
};
