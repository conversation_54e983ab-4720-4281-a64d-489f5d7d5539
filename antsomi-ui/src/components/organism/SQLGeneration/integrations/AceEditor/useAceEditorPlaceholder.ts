import type { Ace } from 'react-ace/node_modules/ace-builds';
import { useCallback, useRef, useEffect } from 'react';
import { useUnmount } from '@antscorp/antsomi-ui/es/hooks';

type Params = {
  triggerTitle: string;
  maxRetries?: number;
  baseDelay?: number;
};

export const useAceEditorPlaceholder = (params?: Params) => {
  const {
    triggerTitle = 'generate SQL with Antsomi',
    maxRetries = 5,
    baseDelay = 100,
  } = params || {};

  const timeoutRef = useRef<ReturnType<typeof setTimeout>>();
  const mutationObserverRef = useRef<MutationObserver | null>(null);
  const editorRef = useRef<Ace.Editor | null>(null);
  const placeholderRef = useRef<HTMLElement | null>(null);
  const triggerRef = useRef<HTMLElement | null>(null);
  const retryCountRef = useRef<number>(0);
  const isDestroyedRef = useRef<boolean>(false);

  const syncPlaceholderEl = useCallback((editor: Ace.Editor) => {
    if (isDestroyedRef.current) return false;

    const selectedEl = editor.renderer.container.querySelector('.ace_placeholder');

    if (selectedEl && selectedEl instanceof HTMLDivElement) {
      placeholderRef.current = selectedEl;
      return true;
    }
    return false;
  }, []);

  const waitForPlaceholder = useCallback(
    (editor: Ace.Editor, retryCount: number = 0): Promise<HTMLElement | null> =>
      new Promise(resolve => {
        if (isDestroyedRef.current) {
          resolve(null);
          return;
        }

        // Try to find placeholder immediately
        if (syncPlaceholderEl(editor)) {
          resolve(placeholderRef.current);
          return;
        }

        // If max retries reached, resolve with null
        if (retryCount >= maxRetries) {
          // eslint-disable-next-line no-console
          console.warn(`AceEditor placeholder not found after ${maxRetries} retries`);
          resolve(null);
          return;
        }

        // Use exponential backoff: baseDelay * 2^retryCount
        const delay = baseDelay * 2 ** retryCount;

        timeoutRef.current = setTimeout(() => {
          if (isDestroyedRef.current) {
            resolve(null);
            return;
          }

          waitForPlaceholder(editor, retryCount + 1).then(resolve);
        }, delay);
      }),
    [syncPlaceholderEl, maxRetries, baseDelay],
  );

  const updatePlaceholderVisibility = useCallback(
    async (forceWait: boolean = false) => {
      const editor = editorRef.current;

      if (!editor || isDestroyedRef.current) return;

      // If placeholder not found and we need to wait for it
      if (!placeholderRef.current || forceWait) {
        clearTimeout(timeoutRef.current);

        const placeholder = await waitForPlaceholder(editor, 0);
        if (!placeholder || isDestroyedRef.current) return;
      }

      if (!placeholderRef.current) return;

      const content = editor.session.getValue();
      const isEmpty = content.length === 0;

      const appendTriggerEl = (placeholderEl: HTMLElement) => {
        // Avoid duplicate triggers
        if (triggerRef.current && placeholderEl.contains(triggerRef.current)) {
          return;
        }

        let triggerEl: HTMLElement;

        if (triggerRef.current) {
          triggerEl = triggerRef.current;
        } else {
          triggerEl = document.createElement('span');
          triggerRef.current = triggerEl;
        }

        triggerEl.classList.add('ace-sqlgen-placeholder');
        triggerEl.innerHTML = ` <u class='trigger-title'>${triggerTitle}</u>`;

        placeholderEl.appendChild(triggerEl);
      };

      if (isEmpty && placeholderRef.current) {
        appendTriggerEl(placeholderRef.current);
      }

      if (!isEmpty && triggerRef.current) {
        triggerRef.current.remove();
        triggerRef.current = null;
        // Don't reset placeholderRef here as it might be reused
      }
    },
    [triggerTitle, waitForPlaceholder],
  );

  // Setup MutationObserver to watch for DOM changes
  const setupMutationObserver = useCallback(
    (editor: Ace.Editor) => {
      if (mutationObserverRef.current) {
        mutationObserverRef.current.disconnect();
      }

      mutationObserverRef.current = new MutationObserver(mutations => {
        if (isDestroyedRef.current) return;

        let shouldUpdate = false;

        mutations.forEach(mutation => {
          // Check if placeholder-related nodes were added/removed
          if (mutation.type === 'childList') {
            const addedNodes = Array.from(mutation.addedNodes);
            const removedNodes = Array.from(mutation.removedNodes);

            const hasPlaceholderChanges = [...addedNodes, ...removedNodes].some(
              node =>
                node instanceof Element &&
                (node.classList?.contains('ace_placeholder') ||
                  node.querySelector?.('.ace_placeholder')),
            );

            if (hasPlaceholderChanges) {
              shouldUpdate = true;
            }
          }
        });

        if (shouldUpdate) {
          // Small delay to ensure DOM is stable
          setTimeout(() => {
            if (!isDestroyedRef.current) {
              updatePlaceholderVisibility(true);
            }
          }, 50);
        }
      });

      // Observe the editor container for changes
      mutationObserverRef.current.observe(editor.renderer.container, {
        childList: true,
        subtree: true,
      });
    },
    [updatePlaceholderVisibility],
  );

  const setAceEditorRef = useCallback(
    (editor: Ace.Editor) => {
      if (isDestroyedRef.current) return;

      editorRef.current = editor;
      retryCountRef.current = 0;

      // Setup mutation observer to watch for DOM changes
      setupMutationObserver(editor);

      // Initial placeholder setup
      updatePlaceholderVisibility(true);

      const eventCb = () => {
        if (!isDestroyedRef.current) {
          updatePlaceholderVisibility();
        }
      };

      editor.on('change', eventCb);
      editor.on('blur', eventCb);
      editor.on('focus', eventCb);
    },
    [updatePlaceholderVisibility, setupMutationObserver],
  );

  // Cleanup function
  const cleanup = useCallback(() => {
    isDestroyedRef.current = true;

    clearTimeout(timeoutRef.current);

    if (mutationObserverRef.current) {
      mutationObserverRef.current.disconnect();
      mutationObserverRef.current = null;
    }

    if (triggerRef.current) {
      triggerRef.current.remove();
      triggerRef.current = null;
    }

    placeholderRef.current = null;
    editorRef.current = null;
  }, []);

  useUnmount(cleanup);

  // Also cleanup on component re-render if needed
  useEffect(
    () => () => {
      if (isDestroyedRef.current) {
        cleanup();
      }
    },
    [cleanup],
  );

  return { setAceEditor: setAceEditorRef };
};
