/**
 * Utility for benchmarking AceEditor placeholder performance
 * This helps measure and compare the effectiveness of different placeholder strategies
 */

export interface PlaceholderBenchmarkResult {
  strategy: string;
  totalAttempts: number;
  successfulAttempts: number;
  failedAttempts: number;
  averageTime: number;
  minTime: number;
  maxTime: number;
  successRate: number;
  timeouts: number;
}

export class PlaceholderBenchmark {
  private results: Map<string, number[]> = new Map();
  private failures: Map<string, number> = new Map();
  private timeouts: Map<string, number> = new Map();

  /**
   * Start timing for a specific strategy
   */
  startTiming(strategy: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (!this.results.has(strategy)) {
        this.results.set(strategy, []);
      }
      
      this.results.get(strategy)!.push(duration);
    };
  }

  /**
   * Record a failure for a strategy
   */
  recordFailure(strategy: string): void {
    const current = this.failures.get(strategy) || 0;
    this.failures.set(strategy, current + 1);
  }

  /**
   * Record a timeout for a strategy
   */
  recordTimeout(strategy: string): void {
    const current = this.timeouts.get(strategy) || 0;
    this.timeouts.set(strategy, current + 1);
  }

  /**
   * Get benchmark results for a strategy
   */
  getResults(strategy: string): PlaceholderBenchmarkResult {
    const times = this.results.get(strategy) || [];
    const failures = this.failures.get(strategy) || 0;
    const timeouts = this.timeouts.get(strategy) || 0;
    const totalAttempts = times.length + failures;
    
    if (times.length === 0) {
      return {
        strategy,
        totalAttempts,
        successfulAttempts: 0,
        failedAttempts: failures,
        averageTime: 0,
        minTime: 0,
        maxTime: 0,
        successRate: 0,
        timeouts,
      };
    }

    const averageTime = times.reduce((sum, time) => sum + time, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    const successRate = totalAttempts > 0 ? (times.length / totalAttempts) * 100 : 0;

    return {
      strategy,
      totalAttempts,
      successfulAttempts: times.length,
      failedAttempts: failures,
      averageTime: Math.round(averageTime * 100) / 100,
      minTime: Math.round(minTime * 100) / 100,
      maxTime: Math.round(maxTime * 100) / 100,
      successRate: Math.round(successRate * 100) / 100,
      timeouts,
    };
  }

  /**
   * Get all results
   */
  getAllResults(): PlaceholderBenchmarkResult[] {
    const strategies = new Set([
      ...this.results.keys(),
      ...this.failures.keys(),
      ...this.timeouts.keys(),
    ]);

    return Array.from(strategies).map(strategy => this.getResults(strategy));
  }

  /**
   * Clear all results
   */
  clear(): void {
    this.results.clear();
    this.failures.clear();
    this.timeouts.clear();
  }

  /**
   * Print comparison table to console
   */
  printComparison(): void {
    const results = this.getAllResults();
    
    if (results.length === 0) {
      console.log('No benchmark results available');
      return;
    }

    console.table(results.map(result => ({
      Strategy: result.strategy,
      'Success Rate (%)': result.successRate,
      'Avg Time (ms)': result.averageTime,
      'Min Time (ms)': result.minTime,
      'Max Time (ms)': result.maxTime,
      'Total Attempts': result.totalAttempts,
      'Failures': result.failedAttempts,
      'Timeouts': result.timeouts,
    })));
  }
}

// Global benchmark instance
export const placeholderBenchmark = new PlaceholderBenchmark();

/**
 * Decorator function to automatically benchmark placeholder operations
 */
export function benchmarkPlaceholder(strategy: string) {
  return function <T extends (...args: any[]) => Promise<any>>(
    target: any,
    propertyName: string,
    descriptor: TypedPropertyDescriptor<T>
  ) {
    const method = descriptor.value!;
    
    descriptor.value = (async function (this: any, ...args: any[]) {
      const endTiming = placeholderBenchmark.startTiming(strategy);
      
      try {
        const result = await method.apply(this, args);
        endTiming();
        return result;
      } catch (error) {
        placeholderBenchmark.recordFailure(strategy);
        throw error;
      }
    }) as T;
  };
}

/**
 * Test utility to simulate rapid editor operations
 */
export async function simulateRapidEditorOperations(
  editor: any,
  operations: number = 10,
  delay: number = 100
): Promise<void> {
  for (let i = 0; i < operations; i++) {
    // Add some text
    editor.session.setValue(`Test content ${i}`);
    await new Promise(resolve => setTimeout(resolve, delay));
    
    // Clear text
    editor.session.setValue('');
    await new Promise(resolve => setTimeout(resolve, delay));
  }
}

/**
 * Performance monitoring utility
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private observers: PerformanceObserver[] = [];

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  startMonitoring(): void {
    // Monitor long tasks
    if ('PerformanceObserver' in window) {
      const longTaskObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) {
            console.warn(`Long task detected: ${entry.duration}ms`, entry);
          }
        }
      });

      try {
        longTaskObserver.observe({ entryTypes: ['longtask'] });
        this.observers.push(longTaskObserver);
      } catch (e) {
        console.log('Long task monitoring not supported');
      }
    }
  }

  stopMonitoring(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}
