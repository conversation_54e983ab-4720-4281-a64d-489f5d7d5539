import type { Ace } from 'react-ace/node_modules/ace-builds';
import { useCallback, useRef } from 'react';
import { useUnmount } from '@antscorp/antsomi-ui/es/hooks';

type Params = {
  triggerTitle: string;
};

/**
 * Legacy version of useAceEditorPlaceholder for comparison
 * This is the original implementation with simple retry mechanism
 */
export const useAceEditorPlaceholderLegacy = (params?: Params) => {
  const { triggerTitle = 'generate SQL with Antsomi' } = params || {};

  const timeoutRef = useRef<ReturnType<typeof setTimeout>>();
  const editorRef = useRef<Ace.Editor | null>(null);
  const placeholderRef = useRef<HTMLElement | null>(null);
  const triggerRef = useRef<HTMLElement | null>(null);

  const syncPlaceholderEl = useCallback((editor: Ace.Editor) => {
    const selectedEl = editor.renderer.container.querySelector('.ace_placeholder');

    if (selectedEl && selectedEl instanceof HTMLDivElement) {
      placeholderRef.current = selectedEl;
    }
  }, []);

  const updatePlaceholderVisibility = useCallback(
    (isRetry: boolean = false) => {
      const editor = editorRef.current;

      if (!editor) return;

      // In case type something for editor has content and re-empty it, updatePlaceholderVisibility will be trigger by "change" event. In this case, althought content is empty but the placeholder node still not renderered in dom, then i use retry machineasm to resolve problem
      if (!placeholderRef.current && !isRetry) {
        clearTimeout(timeoutRef.current);

        timeoutRef.current = setTimeout(() => {
          syncPlaceholderEl(editor);

          updatePlaceholderVisibility();
        }, 300);

        return;
      }

      if (!placeholderRef.current) return;

      const content = editor.session.getValue();
      const isEmpty = content.length === 0;

      const appendTriggerEl = (placeholderEl: HTMLElement) => {
        let triggerEl: HTMLElement;

        if (triggerRef.current) {
          triggerEl = triggerRef.current;
        } else {
          triggerEl = document.createElement('span');
          triggerRef.current = triggerEl;
        }

        triggerEl.classList.add('ace-sqlgen-placeholder');
        triggerEl.innerHTML = ` <u class='trigger-title'>${triggerTitle}</u>`;

        placeholderEl.appendChild(triggerEl);
      };

      if (isEmpty && !triggerRef.current) {
        appendTriggerEl(placeholderRef.current);
      }

      if (!isEmpty) {
        placeholderRef.current = null;
        triggerRef.current = null;
      }
    },
    [editorRef, triggerTitle, syncPlaceholderEl],
  );

  const setAceEditorRef = useCallback(
    (editor: Ace.Editor) => {
      editorRef.current = editor;

      // when we call setAceEditorRef in first time and editor init state is empty, at the moment placeholder is exist in dom
      syncPlaceholderEl(editor);

      updatePlaceholderVisibility();

      const eventCb = () => updatePlaceholderVisibility();

      editor.on('change', eventCb);
      editor.on('blur', eventCb);
      editor.on('focus', eventCb);
    },
    [updatePlaceholderVisibility, syncPlaceholderEl],
  );

  useUnmount(() => {
    triggerRef.current?.remove();
  });

  return { setAceEditor: setAceEditorRef };
};
