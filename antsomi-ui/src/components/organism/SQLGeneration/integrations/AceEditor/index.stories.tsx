import { Meta, StoryObj } from '@storybook/react';
import AceEditor from 'react-ace';
import { useAceEditorPlaceholder } from './useAceEditorPlaceholder';
import { useAceEditorPlaceholderLegacy } from './useAceEditorPlaceholder.legacy';
import './index.scss';

const meta: Meta = {
  title: 'Organism/SQLGeneration/integrations/useAceEditorPlaceholder',
} satisfies Meta;

export default meta;

export const Default: StoryObj = {
  render: () => {
    const { setAceEditor } = useAceEditorPlaceholder();

    return (
      <div style={{ padding: '20px' }}>
        <h3>Improved Version (with MutationObserver & Exponential Backoff)</h3>
        <AceEditor
          onLoad={editor => {
            setAceEditor(editor);
          }}
          placeholder="Start writting SQL or"
          height="200px"
          width="100%"
        />
      </div>
    );
  },
};

export const Legacy: StoryObj = {
  render: () => {
    const { setAceEditor } = useAceEditorPlaceholderLegacy();

    return (
      <div style={{ padding: '20px' }}>
        <h3>Legacy Version (simple retry mechanism)</h3>
        <AceEditor
          onLoad={editor => {
            setAceEditor(editor);
          }}
          placeholder="Start writting SQL or"
          height="200px"
          width="100%"
        />
      </div>
    );
  },
};

export const Comparison: StoryObj = {
  render: () => {
    const { setAceEditor: setImprovedEditor } = useAceEditorPlaceholder({
      triggerTitle: 'generate with improved version'
    });
    const { setAceEditor: setLegacyEditor } = useAceEditorPlaceholderLegacy({
      triggerTitle: 'generate with legacy version'
    });

    return (
      <div style={{ padding: '20px', display: 'flex', gap: '20px', flexDirection: 'column' }}>
        <div>
          <h3>Improved Version</h3>
          <p>Features: MutationObserver, Exponential backoff, Better error handling</p>
          <AceEditor
            onLoad={editor => {
              setImprovedEditor(editor);
            }}
            placeholder="Start writting SQL or"
            height="200px"
            width="100%"
          />
        </div>

        <div>
          <h3>Legacy Version</h3>
          <p>Features: Simple 300ms timeout retry</p>
          <AceEditor
            onLoad={editor => {
              setLegacyEditor(editor);
            }}
            placeholder="Start writting SQL or"
            height="200px"
            width="100%"
          />
        </div>

        <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
          <h4>Test Instructions:</h4>
          <ol>
            <li>Type some text in both editors</li>
            <li>Clear all text (Ctrl+A, Delete)</li>
            <li>Observe how quickly the custom placeholder appears</li>
            <li>Repeat multiple times to test consistency</li>
            <li>Try rapid typing and clearing to test race conditions</li>
          </ol>
        </div>
      </div>
    );
  },
};
