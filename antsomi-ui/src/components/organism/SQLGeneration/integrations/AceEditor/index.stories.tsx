import { Meta, StoryObj } from '@storybook/react';
import AceEditor from 'react-ace';
import { useAceEditorPlaceholder } from './useAceEditorPlaceholder';
import './index.scss';

const meta: Meta = {
  title: 'Organism/SQLGeneration/integrations/useAceEditorPlaceholder',
} satisfies Meta;

export default meta;

export const Default: StoryObj = {
  render: () => {
    const { setAceEditor } = useAceEditorPlaceholder();

    return (
      <AceEditor
        onLoad={editor => {
          setAceEditor(editor);
        }}
        placeholder="Start writting SQL or"
      />
    );
  },
};
