import { SQLGenerationStore } from './store/types';
import { createSelector } from 'reselect';
import { cloneDeep, last } from 'lodash';

// Careful with selectors that return objects or arrays
// They are cached by reference, so if you mutate the returned object or array, you'll mutate the cache
// If you need to mutate the returned object or array, return a new object or array
// Ex: export const selectPreviousCurrentResult = createSelector(
//   [selectAllPreviousResults],
//   previousResults => previousResults.pop(),
// );

export const selectTableSources = createSelector(
  [(state: SQLGenerationStore) => state.tableSources],
  tableSources => tableSources,
);

export const selectSelctedTableSources = createSelector([selectTableSources], tableSources =>
  tableSources.filter(t => t.selected),
);

export const selectIsEditTableSourcesOpen = (state: SQLGenerationStore) =>
  state.isEditTableSourcesOpen;

export const selectIsEditingPrompt = (state: SQLGenerationStore) => state.isEditingPrompt;

export const selectIsOpen = (state: SQLGenerationStore) => state.isOpen;

export const selectPrompt = (state: SQLGenerationStore) => state.prompt;

export const selectIsLoading = (state: SQLGenerationStore) => state.isLoading;

export const selectCurrentResultKey = (state: SQLGenerationStore) => state.currentResultKey;

export const selectResults = (state: SQLGenerationStore) => state.results;

export const selectResultsByKey = (state: SQLGenerationStore) => state.results.byKey;

export const selectResultsAllKeys = (state: SQLGenerationStore) => state.results.allKeys;

export const selectCurrentResult = createSelector(
  [selectResultsByKey, selectCurrentResultKey],
  (resultsByKey, currentResultKey) =>
    currentResultKey ? resultsByKey[currentResultKey] : undefined,
);

export const selectCurrentResultIndex = createSelector(
  [selectCurrentResultKey, selectResultsAllKeys],
  (currentResultKey, allKeys) => allKeys.findIndex(i => i === currentResultKey),
);

export const selectIsEmptyResult = createSelector(
  [selectResultsAllKeys],
  allKeys => allKeys.length === 0,
);

export const selectIsLastResult = createSelector(
  [selectCurrentResultIndex, selectResultsAllKeys],
  (currentResultIndex, allKeys) => currentResultIndex === allKeys.length - 1,
);

export const selectAllResults = createSelector([selectResultsByKey], resultsByKey =>
  Object.values(resultsByKey),
);

export const selectAllPreviousResultKeys = createSelector(
  [selectCurrentResultIndex, selectResultsAllKeys],
  (currentResultIndex, allKeys) => allKeys.slice(0, currentResultIndex),
);

export const selectAllPreviousResults = createSelector(
  [selectAllPreviousResultKeys, selectResultsByKey],
  (allKeys, resultsByKey) => allKeys.map(key => cloneDeep(resultsByKey[key])),
);

export const selectAllNextResultKeys = createSelector(
  [selectCurrentResultIndex, selectResultsAllKeys],
  (currentResultIndex, allKeys) => allKeys.slice(currentResultIndex + 1),
);

export const selectAllNextResults = createSelector(
  [selectAllNextResultKeys, selectResultsByKey],
  (allKeys, resultsByKey) => allKeys.map(key => resultsByKey[key]),
);

export const selectTotalResults = createSelector([selectResultsAllKeys], allKeys => allKeys.length);

export const selectHasResult = createSelector(
  [selectResultsAllKeys, selectCurrentResult],
  (allKeys, currentResult) => allKeys.length > 0 && !!currentResult,
);

export const selectCurrentResultSummary = createSelector(
  [selectCurrentResult],
  currentResult => currentResult?.summary,
);

export const selectIsSummaryLoading = createSelector(
  [(state: SQLGenerationStore) => state.isSummaryLoading],
  isSummaryLoading => isSummaryLoading,
);

export const selectRefinePrompt = createSelector(
  [(state: SQLGenerationStore) => state.refinePrompt],
  refinePrompt => refinePrompt,
);

export const selectIsRefining = createSelector(
  [(state: SQLGenerationStore) => state.isRefining],
  isRefining => isRefining,
);

export const selectResultSettings = createSelector(
  [(state: SQLGenerationStore) => state.results.byKeySettings],
  byKeySettings => byKeySettings,
);

export const selectCurrentResultSettings = createSelector(
  [selectResultSettings, selectCurrentResultKey],
  (byKeySettings, currentResultKey) =>
    currentResultKey ? byKeySettings[currentResultKey] : undefined,
);

export const selectCurrentResultShowDiff = createSelector(
  [selectCurrentResultSettings],
  currentResultSettings => currentResultSettings?.showDiff,
);

export const selectPreviousCurrentResult = createSelector(
  [selectAllPreviousResults],
  previousResults => last(previousResults),
);
