import { createStore } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { devtools } from 'zustand/middleware';
import {
  selectAllPreviousResults,
  selectCurrentResult,
  selectCurrentResultIndex,
  selectCurrentResultSettings,
  selectSelctedTableSources,
} from '../selectors';
import { pick } from 'lodash';
import { DEFAULT_RESULT_SETTINGS } from '../constants';
import produce from 'immer';
import { SQLGenerationState, SQLGenerationStore, SQLGenerationStoreInitialProps } from './types';

export const createSQLGenerationStore = (initProps?: SQLGenerationStoreInitialProps) => {
  const DEFAULT_PROPS: SQLGenerationState = {
    abortControllers: {},
    isOpen: false,
    isLoading: false,
    isEditTableSourcesOpen: false,
    isSummaryLoading: false,
    isEditingPrompt: false,
    isRefining: false,
    currentResultKey: null,
    prompt: '',
    refinePrompt: '',
    results: {
      byKey: {},
      byKeySettings: {},
      allKeys: [],
    },
    tableSources: [],
    searchQuery: '',
    ...initProps,
  };

  return createStore<SQLGenerationStore>()(
    devtools(
      immer((set, get) => ({
        ...DEFAULT_PROPS,

        setIsOpen: isOpen => {
          set({ isOpen });
          get().onOpenChange?.(isOpen);
        },
        setIsLoading: isLoading => set({ isLoading }),
        setIsSummaryLoading: isSummaryLoading => set({ isSummaryLoading }),
        setIsEditTableSourcesOpen: isEditTableSourcesOpen => set({ isEditTableSourcesOpen }),
        setIsEditingPrompt: isEditingPrompt => set({ isEditingPrompt }),
        setCurrentResultKey: newKey => {
          if (newKey !== get().currentResultKey) {
            get().abortQuerySummary();
            get().abortGenerateSQL();

            set({ isEditingPrompt: false });
          }

          set(state => {
            state.currentResultKey = newKey;
          });
        },
        setPrompt: prompt => set({ prompt }),
        setResults: results => set({ results }),
        setTableSources: tableSources => set({ tableSources }),
        setSearchQuery: searchQuery => set({ searchQuery }),

        generateSQL: async () => {
          const { onGenerateSQL, prompt, setIsLoading } = get();

          if (!onGenerateSQL) return;

          const selectedTables = selectSelctedTableSources(get());
          const pastTurns = selectAllPreviousResults(get());

          const controller = new AbortController();

          setIsLoading(true);

          set(state => {
            state.abortControllers.generateSQL = controller;
          });

          try {
            const sqlResult = await onGenerateSQL(
              prompt,
              {
                selectedTables,
                history: { pastTurns },
              },
              {
                signal: controller.signal,
              },
            );

            const currentResultIndex = selectCurrentResultIndex(get());
            const prevCurrentResultSettings = selectCurrentResultSettings(get());

            if (currentResultIndex !== -1) {
              // remove from current results and insert new result
              set(state => {
                state.results.allKeys.splice(currentResultIndex);

                state.results.byKey = pick(state.results.byKey, state.results.allKeys);
                state.results.byKeySettings = pick(
                  state.results.byKeySettings,
                  state.results.allKeys,
                );
              });
            }

            set(state => {
              state.prompt = '';

              state.results.allKeys.push(sqlResult.key);

              state.results.byKey[sqlResult.key] = sqlResult;
              state.results.byKeySettings[sqlResult.key] =
                prevCurrentResultSettings || DEFAULT_RESULT_SETTINGS;

              state.currentResultKey = sqlResult.key;
            });
          } catch (error) {
            if (controller.signal.aborted) return;

            // eslint-disable-next-line no-console
            console.error('Failed to generate SQL:', error);
            // Handle error state if necessary
          } finally {
            setIsLoading(false);

            set(state => {
              state.abortControllers.generateSQL = undefined;
            });
          }
        },

        insertSQL: () => {
          const { onInsertSQL, results, currentResultKey } = get();

          if (!currentResultKey) return;

          const currentSql = results.byKey[currentResultKey].sqlSuggestion;

          if (onInsertSQL && currentSql) {
            onInsertSQL(currentSql);
          }
        },

        querySummary: async () => {
          const { onQuerySummary, results, currentResultKey, setIsSummaryLoading } = get();

          const controller = new AbortController();

          try {
            if (!currentResultKey) return;

            const currentSql = results.byKey[currentResultKey].sqlSuggestion;

            if (!onQuerySummary || !currentSql) return;

            setIsSummaryLoading(true);

            set(state => {
              state.abortControllers.querySummary = controller;
            });

            const summarySqlResult = await onQuerySummary(currentSql, {
              signal: controller.signal,
            });

            set(state => {
              const currentResult = state.results.byKey[currentResultKey];

              if (currentResult) {
                currentResult.summary = summarySqlResult;
              }
            });
          } catch (error) {
            if (controller.signal.aborted) return;

            // eslint-disable-next-line no-console
            console.error('Failed to generate SQL:', error);

            return;
          } finally {
            setIsSummaryLoading(false);

            set(state => {
              state.abortControllers.querySummary = undefined;
            });
          }
        },

        abortGenerateSQL: () => {
          const { abortControllers, setIsLoading } = get();

          abortControllers.generateSQL?.abort();
          setIsLoading(false);
        },

        abortQuerySummary: () => {
          const { abortControllers, setIsSummaryLoading } = get();

          abortControllers.querySummary?.abort();
          setIsSummaryLoading(false);
        },

        abortRefineSQL: () => {
          const { abortControllers, setIsRefining } = get();

          abortControllers.refineSQL?.abort();
          setIsRefining(false);
        },

        refineSQL: async () => {
          const { setIsRefining } = get();

          const onRefineSQL = get().onRefineSQL || get().onGenerateSQL;

          const currentResult = selectCurrentResult(get());
          const previousResults = selectAllPreviousResults(get());

          if (!onRefineSQL || !currentResult) return;

          const pastTurns = [...previousResults, currentResult];

          const controller = new AbortController();

          try {
            setIsRefining(true);

            set(state => {
              state.abortControllers.refineSQL = controller;

              // update current result refine prompt
              state.results.byKey[currentResult.key].refinePrompt = get().refinePrompt;
            });

            const refineResult = await onRefineSQL(
              get().refinePrompt,
              {
                selectedTables: selectSelctedTableSources(get()),
                history: { pastTurns },
              },
              {
                signal: controller.signal,
              },
            );

            const currentResultIndex = selectCurrentResultIndex(get());

            set(state => {
              // remove all results after current result
              state.results.allKeys.splice(currentResultIndex + 1);
              state.results.allKeys.push(refineResult.key);

              state.results.byKey = pick(state.results.byKey, state.results.allKeys);
              state.results.byKeySettings = pick(
                state.results.byKeySettings,
                state.results.allKeys,
              );

              // update refine result
              state.results.byKey[refineResult.key] = refineResult;
              state.results.byKeySettings[refineResult.key] = DEFAULT_RESULT_SETTINGS;

              state.refinePrompt = '';

              state.currentResultKey = refineResult.key;
            });
          } catch (error) {
            if (controller.signal.aborted) return;

            // eslint-disable-next-line no-console
            console.error('Failed to refine SQL:', error);

            return;
          } finally {
            setIsRefining(false);

            set(state => {
              state.abortControllers.refineSQL = undefined;
            });
          }
        },

        setRefinePrompt: refinePrompt => set({ refinePrompt }),

        setIsRefining: isRefining => set({ isRefining }),

        setCurrentResultSettings: updateFn => {
          const { currentResultKey } = get();

          if (!currentResultKey) return;

          set(state => {
            const updated = produce(state.results.byKeySettings[currentResultKey], updateFn);

            state.results.byKeySettings[currentResultKey] = updated;
          });
        },
      })),
      {
        name: 'SQLGenerationStore',
        enabled: initProps?.env !== 'production',
      },
    ),
  );
};
