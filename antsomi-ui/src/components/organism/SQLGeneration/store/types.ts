import { SQLGenerationContext, SQLGenerationResult, TableSource } from '../types';
import { Draft } from 'immer';

export interface SQLResultSettings {
  showDiff: boolean;
}

export interface SQLResults {
  byKey: Record<string, SQLGenerationResult>;
  byKeySettings: Record<string, SQLResultSettings>;
  allKeys: string[];
}

export interface SQLGenerationState {
  abortControllers: {
    generateSQL?: AbortController;
    querySummary?: AbortController;
    refineSQL?: AbortController;
  };

  // Component state
  isOpen: boolean;
  isLoading: boolean;
  isRefining: boolean;
  isSummaryLoading: boolean;
  isEditTableSourcesOpen: boolean;
  isEditingPrompt: boolean;
  currentResultKey: string | null;

  // Data
  prompt: string;
  refinePrompt: string;
  tableSources: TableSource[];
  searchQuery: string;
  results: SQLResults;

  // Callbacks from props
  onGenerateSQL?: (
    prompt: string,
    context: SQLGenerationContext,
    options: { signal: AbortSignal },
  ) => Promise<SQLGenerationResult>;
  onRefineSQL?: (
    refinePrompt: string,
    context: SQLGenerationContext,
    options: { signal: AbortSignal },
  ) => Promise<SQLGenerationResult>;
  onInsertSQL?: (sql: string) => void;
  onQuerySummary?: (sql: string, options: { signal: AbortSignal }) => Promise<string>;
  onOpenChange?: (open: boolean) => void;
}

export interface SQLGenerationActions {
  // Component state actions
  setIsOpen: (isOpen: boolean) => void;
  setIsLoading: (isLoading: boolean) => void;
  setIsSummaryLoading: (isLoading: boolean) => void;
  setIsEditTableSourcesOpen: (isOpen: boolean) => void;
  setIsEditingPrompt: (isEditing: boolean) => void;
  setCurrentResultKey: (key: string) => void;

  // Data actions
  setPrompt: (prompt: string) => void;
  setResults: (results: SQLResults) => void;
  setTableSources: (sources: TableSource[]) => void;
  setSearchQuery: (query: string) => void;
  setRefinePrompt: (prompt: string) => void;
  setIsRefining: (isRefining: boolean) => void;
  setCurrentResultSettings: (updateFn: (draft: Draft<SQLResultSettings>) => void) => void;

  // Logic
  generateSQL: () => Promise<void>;
  insertSQL: () => void;
  querySummary: () => Promise<void>;
  refineSQL: () => Promise<void>;

  abortGenerateSQL: () => void;
  abortQuerySummary: () => void;
  abortRefineSQL: () => void;
}

export type SQLGenerationStore = SQLGenerationState & SQLGenerationActions;

export type SQLGenerationStoreInitialProps = {
  tableSources?: TableSource[];
  onGenerateSQL?: (
    prompt: string,
    context: SQLGenerationContext,
    options: { signal: AbortSignal },
  ) => Promise<SQLGenerationResult>;
  onRefineSQL?: (
    refinePrompt: string,
    context: SQLGenerationContext,
    options: { signal: AbortSignal },
  ) => Promise<SQLGenerationResult>;
  onInsertSQL?: (sql: string) => void;
  onQuerySummary?: (sql: string, options: { signal: AbortSignal }) => Promise<string>;
  onOpenChange?: (open: boolean) => void;
  isOpen?: boolean;
  env?: 'production' | 'staging' | 'development';
};
