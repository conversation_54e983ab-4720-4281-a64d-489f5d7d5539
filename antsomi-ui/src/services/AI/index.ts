import axios from 'axios';
import { CDP_API } from '@antscorp/antsomi-constants';
import { Auth } from '../../types';

export const AIServices = {
  generateSQL: async (params: { data: Record<string, unknown>; auth: Auth }) => {
    const { data, auth } = params;

    const response = await axios.post<{
      data: {
        key: string;
        prompt: string;
        refine_prompt: string;
        sql_suggestion: string;
        summary: string;
      };
    }>(`${CDP_API}/api/v1/sql_generation/generate`, data, {
      params: {
        _token: auth.token,
        _user_id: auth.userId,
        _account_id: auth.accountId,
      },
    });

    return response.data.data;
  },
} as const;
