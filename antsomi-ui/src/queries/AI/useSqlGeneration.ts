import { useMutation } from '@tanstack/react-query';
import { Auth } from '../../types';
import { AIServices } from '../../services/AI';

type UseSqlGenerationParams = {
  auth: Auth;
};

export const useSqlGeneration = (params: UseSqlGenerationParams) => {
  const { auth } = params;

  const {
    mutate: generateSQL,
    mutateAsync: generateSQLAsync,
    isLoading,
    isError,
  } = useMutation({
    mutationFn: async (data: Record<string, unknown>) =>
      AIServices.generateSQL({
        auth,
        data,
      }),
  });

  return { generateSQL, generateSQLAsync, isLoading, isError };
};
